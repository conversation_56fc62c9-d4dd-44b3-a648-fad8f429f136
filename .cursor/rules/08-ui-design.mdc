---
description: 
globs: 
alwaysApply: false
---
**I. Foundation & Clarity: Make it Immediately Understandable**

1.  **Rule: Prioritize Content with a Clean Layout.**

    - **Action:** Eliminate all non-essential elements. Use generous white space (negative space) to let content breathe and guide the user's eye. High-rating apps don't feel cluttered; they feel focused.
    - _Visual Cue:_ Bright, airy, focused on what the user came for.
    - _UX Cue:_ Users instantly grasp the screen's purpose.

2.  **Rule: Ensure Crystal-Clear Readability.**

    - **Action:** Use Apple's system fonts (San Francisco and New York) for native feel and optimal legibility. Implement a strong typographic hierarchy (clear titles, subtitles, body text) and maintain high contrast between text and backgrounds.
    - _Visual Cue:_ Text is effortless to read in all sizes and conditions.
    - _UX Cue:_ Reduces eye strain and improves comprehension.

3.  **Rule: Design Obvious and Predictable Navigation.**
    - **Action:** Stick to standard iOS navigation patterns like tab bars for main sections and navigation bars for hierarchy. Ensure icons are universally understood (SF Symbols are your friend) and labels are concise.
    - _Visual Cue:_ Navigation elements are where users expect them.
    - _UX Cue:_ Users can find their way around instinctively, without thinking.

**II. Visual Polish & Branding: Make it Beautiful and Recognizable**

4.  **Rule: Use Color Purposefully and Consistently.**

    - **Action:** Define a limited, harmonious color palette that reflects your brand. Use accent colors strategically to highlight interactive elements and important information, not just for decoration. Maintain consistency in color usage.
    - _Visual Cue:_ A cohesive, aesthetically pleasing look that reinforces brand identity.
    - _UX Cue:_ Color aids understanding and guides interaction subtly.

5.  **Rule: Employ High-Quality, Pixel-Perfect Visuals.**

    - **Action:** All icons, illustrations, and images must be sharp, clear, and optimized for Retina displays. Avoid blurry or pixelated assets. Ensure visuals are meaningful and enhance the content.
    - _Visual Cue:_ A polished, professional aesthetic that signals quality.
    - _UX Cue:_ Visuals are delightful and aid comprehension.

6.  **Rule: Create Visual Hierarchy and Focus.**
    - **Action:** Use size, weight, color, and placement to clearly differentiate elements and guide the user's attention to the most important information or actions on each screen.
    - _Visual Cue:_ The eye is naturally drawn to primary elements first.
    - _UX Cue:_ Users can scan screens quickly and understand priorities.

**III. Interaction & Usability: Make it Effortless and Engaging**

7.  **Rule: Ensure Generous Touch Targets.**

    - **Action:** Make all interactive elements (buttons, list items, icons) at least 44x44 points. This accommodates imprecise taps and reduces user frustration.
    - _Visual Cue:_ Buttons and controls look comfortably tappable.
    - _UX Cue:_ Reduces errors and makes interaction feel easy.

8.  **Rule: Provide Immediate and Clear Feedback.**

    - **Action:** Acknowledge every user interaction instantly. Use subtle animations for state changes (e.g., button presses), loading indicators for delays, and haptic feedback where appropriate and meaningful.
    - _Visual Cue:_ The app feels responsive and alive.
    - _UX Cue:_ Users feel confident their actions are being registered.

9.  **Rule: Design for Thumbs and Ergonomics.**
    - **Action:** Place frequently used actions and navigation controls within easy reach of the thumb, especially on larger devices. Consider one-handed use.
    - _Visual Cue:_ Key controls are thoughtfully positioned.
    - _UX Cue:_ Comfortable and efficient interaction, reducing physical strain.

**IV. Platform Adherence & Modernity: Make it Feel Native and Current**

10. **Rule: Respect iOS Conventions and Guidelines.**

    - **Action:** Deeply understand and apply Apple's Human Interface Guidelines (HIG). Use standard controls and behaviors wherever possible to leverage users' existing knowledge of the platform.
    - _Visual Cue:_ The app feels like a natural extension of iOS.
    - _UX Cue:_ Lowers the learning curve and builds trust.

11. **Rule: Implement a Refined Dark Mode.**

    - **Action:** Provide a well-thought-out Dark Mode that maintains clarity, contrast, and visual appeal. It's not just inverted colors; it's an adapted aesthetic.
    - _Visual Cue:_ Looks great in both light and dark environments.
    - _UX Cue:_ Offers user choice and viewing comfort in low light.

12. **Rule: Ensure Smooth Performance and Adaptive Layouts.**
    - **Action:** Optimize for fluid animations and quick load times. Design layouts that adapt seamlessly to all supported screen sizes, orientations, and Dynamic Type settings.
    - _Visual Cue:_ Animations are smooth; layouts are always well-composed.
    - _UX Cue:_ The app feels reliable, performant, and accessible to all.