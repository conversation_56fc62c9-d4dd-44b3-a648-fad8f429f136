import SwiftUI
import SwiftData
import ComposableArchitecture

struct LogEntryViewOptimized: View {
    @Bindable var store: StoreOf<LogEntryFeature>
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Category Selection
                    CategorySelectionSection(store: store)
                    
                    // Common Fields
                    CommonFieldsSection(store: store)
                    
                    // Category-specific content
                    if let category = store.selectedCategory {
                        CategorySpecificContent(category: category, store: store)
                    }
                }
                .padding()
            }
            .navigationTitle(store.isEditMode ? "Edit Entry" : "New Entry")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        store.send(.cancelEntry)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        store.send(.saveEntry)
                    }
                    .disabled(store.hasValidationErrors)
                    .fontWeight(.semibold)
                }
            }
        }
    }
}

// MARK: - Category Selection Section

private struct CategorySelectionSection: View {
    @Bindable var store: StoreOf<LogEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Activity Type")
                .font(.headline)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                ForEach(LogCategory.allCases, id: \.self) { category in
                    CategoryButton(
                        category: category,
                        isSelected: store.selectedCategory == category
                    ) {
                        store.send(.categorySelected(category))
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

private struct CategoryButton: View {
    let category: LogCategory
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: category.iconName)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : category.color)
                
                Text(category.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : .primary)
            }
            .frame(height: 60)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? category.color : Color(.systemBackground))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(category.color, lineWidth: isSelected ? 0 : 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Common Fields Section

private struct CommonFieldsSection: View {
    @Bindable var store: StoreOf<LogEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Timestamp
            VStack(alignment: .leading, spacing: 8) {
                Text("Time")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                DatePicker(
                    "Select time",
                    selection: Binding(
                        get: { store.timestamp },
                        set: { store.send(.timestampChanged($0)) }
                    ),
                    displayedComponents: [.date, .hourAndMinute]
                )
                .datePickerStyle(.compact)
            }
            
            // Notes
            VStack(alignment: .leading, spacing: 8) {
                Text("Notes (Optional)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                TextField(
                    "Add any additional notes...",
                    text: Binding(
                        get: { store.notes },
                        set: { store.send(.notesChanged($0)) }
                    ),
                    axis: .vertical
                )
                .textFieldStyle(.roundedBorder)
                .lineLimit(3...6)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

// MARK: - Category-Specific Content

private struct CategorySpecificContent: View {
    let category: LogCategory
    @Bindable var store: StoreOf<LogEntryFeature>
    
    var body: some View {
        switch category {
        case .feeding:
            FeedingEntryForm(store: store.scope(state: \.feedingState, action: \.feeding))
        case .diaper:
            DiaperEntryForm(store: store.scope(state: \.diaperState, action: \.diaper))
        case .sleep:
            SleepEntryForm(store: store.scope(state: \.sleepState, action: \.sleep))
        case .growth:
            GrowthEntryForm(store: store.scope(state: \.growthState, action: \.growth))
        case .health:
            HealthEntryForm(store: store.scope(state: \.healthState, action: \.health))
        }
    }
}

// MARK: - Feeding Entry Form

private struct FeedingEntryForm: View {
    @Bindable var store: StoreOf<FeedingEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Feeding Details")
                .font(.headline)
            
            // Feeding Type Picker
            Picker("Feeding Type", selection: Binding(
                get: { store.feedingType },
                set: { store.send(.feedingTypeChanged($0)) }
            )) {
                ForEach(FeedingEntry.FeedingType.allCases, id: \.self) { type in
                    Text(type.displayName).tag(type)
                }
            }
            .pickerStyle(.segmented)
            
            // Type-specific fields
            switch store.feedingType {
            case .breastfeeding:
                BreastfeedingFields(store: store)
            case .bottleFeeding:
                BottleFeedingFields(store: store)
            case .solidFood:
                SolidFoodFields(store: store)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

private struct BreastfeedingFields: View {
    @Bindable var store: StoreOf<FeedingEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Duration
            VStack(alignment: .leading, spacing: 8) {
                Text("Duration (minutes)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Stepper(
                    value: Binding(
                        get: { store.duration },
                        set: { store.send(.durationChanged($0)) }
                    ),
                    in: 1...120
                ) {
                    Text("\(store.duration) minutes")
                }
            }
            
            // Breast selection
            VStack(alignment: .leading, spacing: 8) {
                Text("Breast(s)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack(spacing: 16) {
                    Toggle("Left", isOn: Binding(
                        get: { store.leftBreast },
                        set: { _ in store.send(.leftBreastToggled) }
                    ))
                    .toggleStyle(.button)
                    
                    Toggle("Right", isOn: Binding(
                        get: { store.rightBreast },
                        set: { _ in store.send(.rightBreastToggled) }
                    ))
                    .toggleStyle(.button)
                }
            }
        }
    }
}

private struct BottleFeedingFields: View {
    @Bindable var store: StoreOf<FeedingEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Volume
            VStack(alignment: .leading, spacing: 8) {
                Text("Volume (ml/oz)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                TextField(
                    "Enter volume",
                    text: Binding(
                        get: { store.volumeText },
                        set: { store.send(.volumeChanged($0)) }
                    )
                )
                .textFieldStyle(.roundedBorder)
                .keyboardType(.decimalPad)
                
                if let error = store.volumeError {
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.red)
                }
            }
            
            // Bottle content
            VStack(alignment: .leading, spacing: 8) {
                Text("Content")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Picker("Content", selection: Binding(
                    get: { store.bottleContent },
                    set: { store.send(.bottleContentChanged($0)) }
                )) {
                    ForEach(FeedingEntry.BottleContent.allCases, id: \.self) { content in
                        Text(content.displayName).tag(content)
                    }
                }
                .pickerStyle(.segmented)
            }
        }
    }
}

private struct SolidFoodFields: View {
    @Bindable var store: StoreOf<FeedingEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Food item
            VStack(alignment: .leading, spacing: 8) {
                Text("Food Item")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                TextField(
                    "What did they eat?",
                    text: Binding(
                        get: { store.foodItem },
                        set: { store.send(.foodItemChanged($0)) }
                    )
                )
                .textFieldStyle(.roundedBorder)
                
                if let error = store.foodItemError {
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.red)
                }
            }
            
            // Amount
            VStack(alignment: .leading, spacing: 8) {
                Text("Amount (Optional)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                TextField(
                    "How much?",
                    text: Binding(
                        get: { store.foodAmount },
                        set: { store.send(.foodAmountChanged($0)) }
                    )
                )
                .textFieldStyle(.roundedBorder)
            }
            
            // Reaction
            VStack(alignment: .leading, spacing: 8) {
                Text("Reaction (Optional)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                TextField(
                    "How did they react?",
                    text: Binding(
                        get: { store.reaction },
                        set: { store.send(.reactionChanged($0)) }
                    )
                )
                .textFieldStyle(.roundedBorder)
            }
        }
    }
}

// MARK: - Diaper Entry Form

private struct DiaperEntryForm: View {
    @Bindable var store: StoreOf<DiaperEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Diaper Details")
                .font(.headline)
            
            // Diaper type
            VStack(alignment: .leading, spacing: 8) {
                Text("Type")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Picker("Type", selection: Binding(
                    get: { store.diaperType },
                    set: { store.send(.diaperTypeChanged($0)) }
                )) {
                    ForEach(DiaperEntry.DiaperType.allCases, id: \.self) { type in
                        Text(type.displayName).tag(type)
                    }
                }
                .pickerStyle(.segmented)
            }
            
            // Poop details (if applicable)
            if store.diaperType == .dirty || store.diaperType == .mixed {
                PoopDetailsSection(store: store)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

private struct PoopDetailsSection: View {
    @Bindable var store: StoreOf<DiaperEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Color
            VStack(alignment: .leading, spacing: 8) {
                Text("Color")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Picker("Color", selection: Binding(
                    get: { store.poopColor },
                    set: { store.send(.poopColorChanged($0)) }
                )) {
                    ForEach(DiaperEntry.PoopColor.allCases, id: \.self) { color in
                        Text(color.displayName).tag(color)
                    }
                }
                .pickerStyle(.menu)
            }
            
            // Consistency
            VStack(alignment: .leading, spacing: 8) {
                Text("Consistency")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Picker("Consistency", selection: Binding(
                    get: { store.poopConsistency },
                    set: { store.send(.poopConsistencyChanged($0)) }
                )) {
                    ForEach(DiaperEntry.PoopConsistency.allCases, id: \.self) { consistency in
                        Text(consistency.displayName).tag(consistency)
                    }
                }
                .pickerStyle(.menu)
            }
        }
    }
}

// MARK: - Sleep Entry Form

private struct SleepEntryForm: View {
    @Bindable var store: StoreOf<SleepEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Sleep Details")
                .font(.headline)
            
            // Start time
            VStack(alignment: .leading, spacing: 8) {
                Text("Start Time")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                DatePicker(
                    "Start time",
                    selection: Binding(
                        get: { store.startTime },
                        set: { store.send(.startTimeChanged($0)) }
                    ),
                    displayedComponents: [.date, .hourAndMinute]
                )
                .datePickerStyle(.compact)
            }
            
            // Ongoing toggle
            Toggle("Sleep is ongoing", isOn: Binding(
                get: { store.isOngoing },
                set: { _ in store.send(.ongoingToggled) }
            ))
            
            // End time (if not ongoing)
            if !store.isOngoing {
                VStack(alignment: .leading, spacing: 8) {
                    Text("End Time")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    DatePicker(
                        "End time",
                        selection: Binding(
                            get: { store.endTime },
                            set: { store.send(.endTimeChanged($0)) }
                        ),
                        displayedComponents: [.date, .hourAndMinute]
                    )
                    .datePickerStyle(.compact)
                    
                    if store.hasValidationErrors {
                        Text("End time must be after start time")
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                }
            }
            
            // Location
            VStack(alignment: .leading, spacing: 8) {
                Text("Location")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Picker("Location", selection: Binding(
                    get: { store.location },
                    set: { store.send(.locationChanged($0)) }
                )) {
                    ForEach(SleepEntry.SleepLocation.allCases, id: \.self) { location in
                        Text(location.displayName).tag(location)
                    }
                }
                .pickerStyle(.menu)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

// MARK: - Growth Entry Form

private struct GrowthEntryForm: View {
    @Bindable var store: StoreOf<GrowthEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Growth Measurements")
                .font(.headline)
            
            // Weight
            MeasurementField(
                title: "Weight",
                placeholder: "Enter weight",
                text: Binding(
                    get: { store.weightText },
                    set: { store.send(.weightChanged($0)) }
                ),
                error: store.weightError,
                unit: "kg/lbs"
            )
            
            // Height
            MeasurementField(
                title: "Height",
                placeholder: "Enter height",
                text: Binding(
                    get: { store.heightText },
                    set: { store.send(.heightChanged($0)) }
                ),
                error: store.heightError,
                unit: "cm/in"
            )
            
            // Head circumference
            MeasurementField(
                title: "Head Circumference",
                placeholder: "Enter head circumference",
                text: Binding(
                    get: { store.headCircumferenceText },
                    set: { store.send(.headCircumferenceChanged($0)) }
                ),
                error: store.headCircumferenceError,
                unit: "cm/in"
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

private struct MeasurementField: View {
    let title: String
    let placeholder: String
    @Binding var text: String
    let error: String?
    let unit: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("\(title) (\(unit))")
                .font(.subheadline)
                .fontWeight(.medium)
            
            TextField(placeholder, text: $text)
                .textFieldStyle(.roundedBorder)
                .keyboardType(.decimalPad)
            
            if let error = error {
                Text(error)
                    .font(.caption)
                    .foregroundColor(.red)
            }
        }
    }
}

// MARK: - Health Entry Form

private struct HealthEntryForm: View {
    @Bindable var store: StoreOf<HealthEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Health Details")
                .font(.headline)
            
            // Entry type
            VStack(alignment: .leading, spacing: 8) {
                Text("Type")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Picker("Type", selection: Binding(
                    get: { store.entryType },
                    set: { store.send(.entryTypeChanged($0)) }
                )) {
                    ForEach(HealthEntry.HealthEntryType.allCases, id: \.self) { type in
                        Text(type.displayName).tag(type)
                    }
                }
                .pickerStyle(.menu)
            }
            
            // Type-specific fields
            switch store.entryType {
            case .temperature:
                TemperatureFields(store: store)
            case .medication:
                MedicationFields(store: store)
            case .symptoms:
                SymptomsFields(store: store)
            case .vaccination:
                VaccinationFields(store: store)
            case .appointment:
                AppointmentFields(store: store)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

private struct TemperatureFields: View {
    @Bindable var store: StoreOf<HealthEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Temperature")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack {
                    TextField(
                        "Enter temperature",
                        text: Binding(
                            get: { store.temperatureText },
                            set: { store.send(.temperatureChanged($0)) }
                        )
                    )
                    .textFieldStyle(.roundedBorder)
                    .keyboardType(.decimalPad)
                    
                    Picker("Unit", selection: Binding(
                        get: { store.temperatureUnit },
                        set: { store.send(.temperatureUnitChanged($0)) }
                    )) {
                        ForEach(HealthEntry.TemperatureUnit.allCases, id: \.self) { unit in
                            Text(unit.symbol).tag(unit)
                        }
                    }
                    .pickerStyle(.segmented)
                    .frame(width: 100)
                }
                
                if let error = store.temperatureError {
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.red)
                }
            }
        }
    }
}

private struct MedicationFields: View {
    @Bindable var store: StoreOf<HealthEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Medication Name")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                TextField(
                    "Enter medication name",
                    text: Binding(
                        get: { store.medicationName },
                        set: { store.send(.medicationNameChanged($0)) }
                    )
                )
                .textFieldStyle(.roundedBorder)
                
                if let error = store.medicationNameError {
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.red)
                }
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Dosage (Optional)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                TextField(
                    "Enter dosage",
                    text: Binding(
                        get: { store.medicationDosage },
                        set: { store.send(.medicationDosageChanged($0)) }
                    )
                )
                .textFieldStyle(.roundedBorder)
            }
        }
    }
}

private struct SymptomsFields: View {
    @Bindable var store: StoreOf<HealthEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Symptoms")
                .font(.subheadline)
                .fontWeight(.medium)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                ForEach(HealthEntry.Symptom.allCases, id: \.self) { symptom in
                    Toggle(symptom.displayName, isOn: Binding(
                        get: { store.selectedSymptoms.contains(symptom) },
                        set: { _ in store.send(.symptomToggled(symptom)) }
                    ))
                    .toggleStyle(.button)
                }
            }
        }
    }
}

private struct VaccinationFields: View {
    @Bindable var store: StoreOf<HealthEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Vaccine Name")
                .font(.subheadline)
                .fontWeight(.medium)
            
            TextField(
                "Enter vaccine name",
                text: Binding(
                    get: { store.vaccineName },
                    set: { store.send(.vaccineNameChanged($0)) }
                )
            )
            .textFieldStyle(.roundedBorder)
            
            if let error = store.vaccineNameError {
                Text(error)
                    .font(.caption)
                    .foregroundColor(.red)
            }
        }
    }
}

private struct AppointmentFields: View {
    @Bindable var store: StoreOf<HealthEntryFeature>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Reason")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                TextField(
                    "Reason for appointment",
                    text: Binding(
                        get: { store.appointmentReason },
                        set: { store.send(.appointmentReasonChanged($0)) }
                    )
                )
                .textFieldStyle(.roundedBorder)
                
                if let error = store.appointmentReasonError {
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.red)
                }
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Provider (Optional)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                TextField(
                    "Doctor or clinic name",
                    text: Binding(
                        get: { store.appointmentProvider },
                        set: { store.send(.appointmentProviderChanged($0)) }
                    )
                )
                .textFieldStyle(.roundedBorder)
            }
        }
    }
}

// MARK: - Extensions for Display Names

extension FeedingEntry.FeedingType {
    var displayName: String {
        switch self {
        case .breastfeeding: return "Breastfeeding"
        case .bottleFeeding: return "Bottle"
        case .solidFood: return "Solid Food"
        }
    }
}

extension FeedingEntry.BottleContent {
    var displayName: String {
        switch self {
        case .formula: return "Formula"
        case .breastMilk: return "Breast Milk"
        case .water: return "Water"
        case .juice: return "Juice"
        case .other: return "Other"
        }
    }
}

extension DiaperEntry.DiaperType {
    var displayName: String {
        switch self {
        case .wet: return "Wet"
        case .dirty: return "Dirty"
        case .mixed: return "Mixed"
        case .dry: return "Dry"
        }
    }
}

extension DiaperEntry.PoopColor {
    var displayName: String {
        switch self {
        case .yellow: return "Yellow"
        case .brown: return "Brown"
        case .green: return "Green"
        case .black: return "Black"
        case .red: return "Red"
        case .white: return "White"
        }
    }
}

extension DiaperEntry.PoopConsistency {
    var displayName: String {
        switch self {
        case .liquid: return "Liquid"
        case .soft: return "Soft"
        case .seedy: return "Seedy"
        case .firm: return "Firm"
        case .hard: return "Hard"
        }
    }
}

extension SleepEntry.SleepLocation {
    var displayName: String {
        switch self {
        case .crib: return "Crib"
        case .bassinet: return "Bassinet"
        case .parentBed: return "Parent's Bed"
        case .stroller: return "Stroller"
        case .carSeat: return "Car Seat"
        case .other: return "Other"
        }
    }
}

extension HealthEntry.HealthEntryType {
    var displayName: String {
        switch self {
        case .temperature: return "Temperature"
        case .medication: return "Medication"
        case .symptoms: return "Symptoms"
        case .vaccination: return "Vaccination"
        case .appointment: return "Appointment"
        }
    }
}

extension HealthEntry.TemperatureUnit {
    var symbol: String {
        switch self {
        case .celsius: return "°C"
        case .fahrenheit: return "°F"
        }
    }
}

extension HealthEntry.Symptom {
    var displayName: String {
        switch self {
        case .fever: return "Fever"
        case .cough: return "Cough"
        case .runnyNose: return "Runny Nose"
        case .rash: return "Rash"
        case .vomiting: return "Vomiting"
        case .diarrhea: return "Diarrhea"
        case .fussy: return "Fussy"
        case .lethargic: return "Lethargic"
        }
    }
} 