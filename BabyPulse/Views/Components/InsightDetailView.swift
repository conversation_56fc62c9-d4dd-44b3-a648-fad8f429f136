//
//  InsightDetailView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import SwiftData

struct InsightDetailView: View {
    let insight: Insight
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.modelContext) private var modelContext

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingLG) {
                // Header
                HStack {
                    ZStack {
                        Circle()
                            .fill(insight.category.color.opacity(0.2))
                            .frame(width: 40, height: 40)

                        Image(systemName: insight.category.icon)
                            .font(.system(size: 20))
                            .foregroundColor(insight.category.color)
                    }

                    VStack(alignment: .leading) {
                        Text(insight.title)
                            .font(BabyPulseTypography.title3())
                            .foregroundColor(insight.category.color)

                        Text(formattedDate)
                            .font(BabyPulseTypography.caption())
                            .foregroundColor(BabyPulseColors.textSecondary)
                    }

                    Spacer()

                    // Attention indicator
                    if insight.needsAttention {
                        Label("Needs Attention", systemImage: "exclamationmark.triangle.fill")
                            .font(BabyPulseTypography.caption())
                            .foregroundColor(.orange)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(Color.orange.opacity(0.1))
                            )
                    }
                }

                // Metric
                Text(insight.metric)
                    .font(BabyPulseTypography.title2())
                    .foregroundColor(BabyPulseColors.primary)

                // Description
                MarkdownText(content: insight.insightContent, fontSize: 16)
                    .foregroundColor(BabyPulseColors.text)

                // Feedback Section
                HStack(spacing: BabyPulseLayout.spacingLG) {
                    Text("Was this insight helpful?")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.textSecondary)

                    Spacer()

                    Button(action: {
                        toggleFeedback(helpful: true)
                    }) {
                        Label("Yes", systemImage: insight.feedback == true ? "hand.thumbsup.fill" : "hand.thumbsup")
                            .font(BabyPulseTypography.body())
                            .foregroundColor(insight.feedback == true ? BabyPulseColors.success : BabyPulseColors.textSecondary)
                    }

                    Button(action: {
                        toggleFeedback(helpful: false)
                    }) {
                        Label("No", systemImage: insight.feedback == false ? "hand.thumbsdown.fill" : "hand.thumbsdown")
                            .font(BabyPulseTypography.body())
                            .foregroundColor(insight.feedback == false ? BabyPulseColors.error : BabyPulseColors.textSecondary)
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                        .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color(hex: "F5F5F7"))
                )

                // Confidence
                HStack {
                    Text("Confidence")
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(BabyPulseColors.text)

                    Spacer()

                    Text("\(insight.confidence)%")
                        .font(BabyPulseTypography.bodyBold())
                        .foregroundColor(confidenceColor)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                        .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color(hex: "F5F5F7"))
                )

                // Related data section
                Text("Based on")
                    .font(BabyPulseTypography.title3())
                    .foregroundColor(BabyPulseColors.text)
                    .padding(.top)

                VStack(alignment: .leading, spacing: BabyPulseLayout.spacingXS) {
                    Text("This insight is derived from \(insight.category.displayName.lowercased()) data logged around \(formattedInsightDateForContext()).")
                        .font(BabyPulseTypography.body())
                        .foregroundColor(BabyPulseColors.textSecondary)

                    // Placeholder for future linked entries
                    Text("Specific entries will be linked here in a future update.")
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(BabyPulseColors.textTertiary)
                        .padding(.top, BabyPulseLayout.spacingSM)
                }
                .padding(BabyPulseLayout.paddingMD)
                .background(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                        .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color(hex: "F5F5F7"))
                )

                Spacer()
            }
            .padding(BabyPulseLayout.paddingLG)
        }
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .foregroundColor(BabyPulseColors.primary)
                }
            }

            ToolbarItem(placement: .principal) {
                Text("Insight")
                    .font(BabyPulseTypography.title3())
                    .foregroundColor(BabyPulseColors.text)
            }
        }
        .onAppear {
            // Mark insight as read
            markAsRead()
        }
    }

    private var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: insight.timestamp)
    }

    private func formattedInsightDateForContext() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .long // e.g., "October 23, 2024"
        return formatter.string(from: insight.timestamp)
    }

    private var confidenceColor: Color {
        if insight.confidence >= 90 {
            return .green
        } else if insight.confidence >= 70 {
            return .orange
        } else {
            return .red
        }
    }

    private func markAsRead() {
        // Insight is a @Model class, so we can modify it directly
        // and save the context.
        if !insight.isRead {
            insight.isRead = true
            // Save context is handled by toggleFeedback or other actions if needed, or on disappear.
            // For just marking as read, it's fine to save here if no other action saves.
            saveInsight()
        }
    }

    private func toggleFeedback(helpful: Bool) {
        if insight.feedback == helpful {
            insight.feedback = nil // Toggle off if same button pressed
        } else {
            insight.feedback = helpful
        }
        saveInsight()
    }

    private func saveInsight() {
        do {
            try modelContext.save()
            print("Insight \(insight.id) updated and saved.")
        } catch {
            print("Failed to save insight \(insight.id): \(error.localizedDescription)")
        }
    }
}

#Preview {
    NavigationView {
        InsightDetailView(
            insight: Insight(
                id: UUID(),
                category: .feeding,
                title: "Feeding Pattern",
                metric: "6 feedings today",
                insightContent: "Your baby is feeding well with a good mix of breast and bottle feedings. The average time between feedings is about 3 hours, which is appropriate for their age. Consider offering more frequent feedings during growth spurts.",
                timestamp: Date().addingTimeInterval(-3600),
                isRead: false,
                needsAttention: true,
                confidence: 85,
                feedback: nil // Add feedback to preview
            )
        )
    }
}
