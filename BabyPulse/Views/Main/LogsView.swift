import SwiftUI
import SwiftData
import ComposableArchitecture

struct LogsViewOptimized: View {
    @Bindable var store: StoreOf<LogsFeature>

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Date Navigation Header
                DateNavigationHeader(store: store)

                // Filter Controls
                FilterControlsView(store: store)

                // Content Area
                if store.isLoading {
                    LoadingView()
                } else if let errorMessage = store.errorMessage {
                    ErrorView(message: errorMessage) {
                        store.send(.loadEntries)
                    }
                } else {
                    LogsContentView(store: store)
                }
            }
            .navigationTitle("Activity Log")
            .navigationBarTitleDisplayMode(.large)
        }
        .onAppear {
            store.send(.loadEntries)
        }
    }
}

// MARK: - Sub-Components

private struct DateNavigationHeader: View {
    @Bindable var store: StoreOf<LogsFeature>

    var body: some View {
        HStack {
            Button(action: { store.send(.navigateDate(.backward)) }) {
                Image(systemName: "chevron.left")
                    .font(.title2)
                    .foregroundColor(.primary)
            }

            Spacer()

            VStack {
                Text(store.formattedSelectedDate)
                    .font(.headline)
                    .fontWeight(.semibold)

                if store.isToday {
                    Text("Today")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .onTapGesture {
                store.send(.dateSelected(Date()))
            }

            Spacer()

            Button(action: { store.send(.navigateDate(.forward)) }) {
                Image(systemName: "chevron.right")
                    .font(.title2)
                    .foregroundColor(.primary)
            }
            .disabled(store.isToday)
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
    }
}

private struct FilterControlsView: View {
    @Bindable var store: StoreOf<LogsFeature>

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(LogCategory.allCases, id: \.self) { category in
                    FilterChip(
                        category: category,
                        isSelected: store.activeFilters.contains(category)
                    ) {
                        store.send(.filterToggled(category))
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
}

private struct FilterChip: View {
    let category: LogCategory
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: category.iconName)
                    .font(.caption)
                Text(category.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? Color.accentColor : Color(.systemGray5))
            )
            .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

private struct LogsContentView: View {
    @Bindable var store: StoreOf<LogsFeature>

    var body: some View {
        if store.logEntries.isEmpty {
            EmptyStateView()
        } else {
            TimelineView(store: store)
        }
    }
}

private struct TimelineView: View {
    @Bindable var store: StoreOf<LogsFeature>

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(store.timeSlots, id: \.0) { hour, entries in
                    TimeSlotSection(hour: hour, entries: entries) { entry in
                        store.send(.editEntry(entry))
                    }
                }
            }
            .padding()
        }
    }
}

private struct TimeSlotSection: View {
    let hour: Int
    let entries: [TimelineEntry]
    let onEditEntry: (TimelineEntry) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(timeString)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)

                Spacer()

                Text("\(entries.count) \(entries.count == 1 ? "entry" : "entries")")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            VStack(spacing: 8) {
                ForEach(entries, id: \.id) { entry in
                    TimelineEntryCard(entry: entry) {
                        onEditEntry(entry)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }

    private var timeString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "h:mm a"
        let date = Calendar.current.date(bySettingHour: hour, minute: 0, second: 0, of: Date()) ?? Date()
        return formatter.string(from: date)
    }
}

private struct TimelineEntryCard: View {
    let entry: TimelineEntry
    let onEdit: () -> Void

    var body: some View {
        HStack {
            // Category Icon
            Image(systemName: entry.category.iconName)
                .font(.title3)
                .foregroundColor(entry.category.color)
                .frame(width: 24, height: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(entry.title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                if !entry.subtitle.isEmpty {
                    Text(entry.subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            Text(entry.timeString)
                .font(.caption)
                .foregroundColor(.secondary)

            Button(action: onEdit) {
                Image(systemName: "ellipsis")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6))
        )
    }
}

private struct LoadingView: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)

            Text("Loading entries...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

private struct ErrorView: View {
    let message: String
    let onRetry: () -> Void

    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.largeTitle)
                .foregroundColor(.orange)

            Text("Something went wrong")
                .font(.headline)

            Text(message)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button("Try Again", action: onRetry)
                .buttonStyle(.borderedProminent)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

private struct EmptyStateView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "calendar.badge.plus")
                .font(.system(size: 48))
                .foregroundColor(.secondary)

            Text("No entries for this day")
                .font(.headline)

            Text("Start tracking your baby's activities to see them here")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Extensions

extension LogCategory {
    var iconName: String {
        switch self {
        case .feeding: return "bottle"
        case .diaper: return "circle.grid.2x2"
        case .sleep: return "moon.zzz"
        case .growth: return "ruler"
        case .health: return "heart.text.square"
        case .general: return "figure.walk"
        }
    }

    var displayName: String {
        switch self {
        case .feeding: return "Feeding"
        case .diaper: return "Diaper"
        case .sleep: return "Sleep"
        case .growth: return "Growth"
        case .health: return "Health"
        case .general: return "Activity"
        }
    }

    // Note: Using the color property from LogCategory.swift
}

extension TimelineEntry {
    var timeString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "h:mm a"
        return formatter.string(from: timestamp)
    }
}