import SwiftUI
import ComposableArchitecture

struct InsightsViewOptimized: View {
    @Bindable var store: StoreOf<InsightsFeature>

    var body: some View {
        NavigationStack {
            ScrollView {
                LazyVStack(spacing: 20) {
                    if store.isLoading {
                        LoadingSection()
                    } else if let errorMessage = store.errorMessage {
                        ErrorSection(message: errorMessage) {
                            store.send(.refreshInsights)
                        }
                    } else {
                        MainContent(store: store)
                    }
                }
                .padding(.horizontal)
            }
            .navigationTitle("Insights")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                store.send(.refreshInsights)
            }
            .onAppear {
                store.send(.onAppear)
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        store.send(.showFilterSheet)
                    } label: {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                    }
                }
            }
        }
        .trackPerformance(viewName: "InsightsView")
        .sheet(isPresented: $store.showingFilterSheet) {
            FilterSheet(store: store)
        }
        .sheet(isPresented: $store.showingInsightDetail) {
            if let insight = store.selectedInsight {
                InsightDetailView(insight: insight) {
                    store.send(.dismissInsightDetail)
                }
            }
        }
    }
}

// MARK: - Main Content
private struct MainContent: View {
    @Bindable var store: StoreOf<InsightsFeature>

    var body: some View {
        VStack(spacing: 24) {
            if store.hasInsights {
                InsightsSummarySection(store: store)
                CategoryFilterSection(store: store)
                InsightsListSection(store: store)
            } else {
                EmptyInsightsSection()
            }
        }
    }
}

// MARK: - Insights Summary Section
private struct InsightsSummarySection: View {
    @Bindable var store: StoreOf<InsightsFeature>

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Summary")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Text(store.timeRange.displayName)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color(.systemGray6))
                    .clipShape(Capsule())
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                SummaryCardView(
                    title: "Total Insights",
                    value: "\(store.filteredInsights.count)",
                    icon: "lightbulb.fill",
                    color: .blue
                )

                SummaryCardView(
                    title: "Needs Attention",
                    value: "\(store.needsAttentionCount)",
                    icon: "exclamationmark.triangle.fill",
                    color: .orange
                )

                SummaryCardView(
                    title: "Categories",
                    value: "\(store.categoryCounts.count)",
                    icon: "folder.fill",
                    color: .green
                )
            }
        }
    }
}

// MARK: - Category Filter Section
private struct CategoryFilterSection: View {
    @Bindable var store: StoreOf<InsightsFeature>

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Categories")
                .font(.headline)
                .fontWeight(.semibold)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    // All categories filter
                    FilterChip(
                        title: "All",
                        count: store.insights.count,
                        isSelected: store.selectedCategory == nil,
                        color: .blue
                    ) {
                        store.send(.categorySelected(nil))
                    }

                    // Individual category filters
                    ForEach(Insight.InsightCategory.allCases, id: \.self) { category in
                        if let count = store.categoryCounts[category], count > 0 {
                            FilterChip(
                                title: category.displayName,
                                count: count,
                                isSelected: store.selectedCategory == category,
                                color: category.color
                            ) {
                                store.send(.categorySelected(category))
                            }
                        }
                    }
                }
                .padding(.horizontal)
            }
            .padding(.horizontal, -16)
        }
    }
}

// MARK: - Insights List Section
private struct InsightsListSection: View {
    @Bindable var store: StoreOf<InsightsFeature>

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Insights")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Menu {
                    ForEach(InsightsFeature.SortOption.allCases, id: \.self) { option in
                        Button {
                            store.send(.sortOptionChanged(option))
                        } label: {
                            HStack {
                                Text(option.displayName)
                                if store.sortOption == option {
                                    Image(systemName: "checkmark")
                                }
                            }
                        }
                    }
                } label: {
                    HStack(spacing: 4) {
                        Image(systemName: store.sortOption.icon)
                        Text("Sort")
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }

            LazyVStack(spacing: 12) {
                ForEach(store.filteredInsights, id: \.id) { insight in
                    InsightRowView(insight: insight) {
                        store.send(.insightSelected(insight))
                    }
                }
            }
        }
    }
}

// MARK: - Supporting Views



private struct FilterChip: View {
    let title: String
    let count: Int
    let isSelected: Bool
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text("\(count)")
                    .font(.caption)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(isSelected ? Color.white.opacity(0.3) : color.opacity(0.2))
                    )
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                Capsule()
                    .fill(isSelected ? color : Color(.systemGray6))
            )
            .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

private struct InsightRowView: View {
    let insight: Insight
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                VStack {
                    Image(systemName: insight.category.icon)
                        .font(.title3)
                        .foregroundColor(insight.severity.color)

                    if insight.needsAttention {
                        Circle()
                            .fill(Color.red)
                            .frame(width: 8, height: 8)
                    }
                }
                .frame(width: 40)

                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(insight.category.displayName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .textCase(.uppercase)

                        Spacer()

                        Text(insight.timestamp.formatted(.relative(presentation: .named)))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }

                    Text(insight.title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .multilineTextAlignment(.leading)
                        .lineLimit(2)

                    Text(insight.summary)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(3)
                        .multilineTextAlignment(.leading)

                    HStack {
                        // Confidence indicator
                        HStack(spacing: 4) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.caption2)
                                .foregroundColor(insight.confidence > 0.8 ? .green : .orange)

                            Text("\(Int(insight.confidence * 100))%")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        // Severity indicator
                        HStack(spacing: 4) {
                            Circle()
                                .fill(insight.severity.color)
                                .frame(width: 6, height: 6)

                            Text(insight.severity.displayName)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(insight.needsAttention ? Color.red.opacity(0.3) : Color(.systemGray6), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Empty State
private struct EmptyInsightsSection: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "lightbulb")
                .font(.system(size: 60))
                .foregroundColor(.gray.opacity(0.6))

            VStack(spacing: 8) {
                Text("No Insights Yet")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Keep logging your baby's activities to get personalized insights about their patterns and development.")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            NavigationLink("Start Logging Activities") {
                // Navigate to logs view
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .padding(.vertical, 40)
    }
}

// MARK: - Loading and Error States
private struct LoadingSection: View {
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.2)

            Text("Loading insights...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 40)
    }
}

private struct ErrorSection: View {
    let message: String
    let onRetry: () -> Void

    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.title)
                .foregroundColor(.orange)

            Text("Unable to Load Insights")
                .font(.headline)
                .fontWeight(.semibold)

            Text(message)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button("Try Again", action: onRetry)
                .buttonStyle(.borderedProminent)
        }
        .padding(.vertical, 40)
    }
}

// MARK: - Filter Sheet
private struct FilterSheet: View {
    @Bindable var store: StoreOf<InsightsFeature>

    var body: some View {
        NavigationStack {
            Form {
                Section("Time Range") {
                    Picker("Time Range", selection: $store.timeRange) {
                        ForEach(InsightsFeature.TimeRange.allCases, id: \.self) { range in
                            Text(range.displayName).tag(range)
                        }
                    }
                    .pickerStyle(.segmented)
                }

                Section("Sort By") {
                    Picker("Sort Option", selection: $store.sortOption) {
                        ForEach(InsightsFeature.SortOption.allCases, id: \.self) { option in
                            Label(option.displayName, systemImage: option.icon).tag(option)
                        }
                    }
                }

                Section("Categories") {
                    ForEach(Insight.InsightCategory.allCases, id: \.self) { category in
                        HStack {
                            Image(systemName: category.icon)
                                .foregroundColor(category.color)

                            Text(category.displayName)

                            Spacer()

                            if let count = store.categoryCounts[category] {
                                Text("\(count)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            if store.selectedCategory == category {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.blue)
                            }
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            store.send(.categorySelected(store.selectedCategory == category ? nil : category))
                        }
                    }
                }
            }
            .navigationTitle("Filter Insights")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        store.send(.hideFilterSheet)
                    }
                }
            }
        }
    }
}

// MARK: - Summary Card View
private struct SummaryCardView: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.title3)
                .fontWeight(.bold)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray5), lineWidth: 1)
        )
    }
}



// MARK: - Extensions

#Preview {
    InsightsViewOptimized(
        store: Store(initialState: InsightsFeature.State()) {
            InsightsFeature()
        }
    )
}