//
//  HomeView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import SwiftData

struct HomeView: View {
    @StateObject private var viewModel: HomeViewModel
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) private var colorScheme
    @State private var isRefreshing = false

    // Animation states
    @State private var hasAppeared = false
    @State private var scrollOffset: CGFloat = 0

    // State for Quick Action navigation
    @State private var showingLogEntrySheet = false
    @State private var selectedLogCategory: LogCategory? = nil

    init() {
        _viewModel = StateObject(wrappedValue: HomeViewModel())
    }

    var body: some View {
        ZStack {
            // Background
            BabyPulseColors.background
                .ignoresSafeArea()

            ScrollView {
                LazyVStack(spacing: BabyPulseLayout.spacingLG, pinnedViews: []) {
                    // Scroll offset detector
                    GeometryReader { geometry in
                        Color.clear
                            .preference(key: ScrollOffsetPreferenceKey.self,
                                      value: geometry.frame(in: .global).minY)
                    }
                    .frame(height: 0)

                    // Baby profile header
                    if let baby = viewModel.currentBaby {
                        ModernBabyProfileHeader(
                            babyName: baby.name,
                            babyAge: formatBabyAge(birthDate: baby.birthDate),
                            profileImage: baby.photoData != nil ? UIImage(data: baby.photoData!) : nil,
                            scrollOffset: scrollOffset
                        )
                        .padding(.top, BabyPulseLayout.paddingMD)
                        .transition(.opacity.combined(with: .move(edge: .top)))
                    }

                    // Today's summary section
                    VStack(spacing: BabyPulseLayout.spacingMD) {
                        ModernTodaySummaryCard(summaryData: convertToDict(viewModel.todaySummary))
                            .transition(.opacity.combined(with: .scale))
                    }
                    .padding(.horizontal, BabyPulseLayout.paddingLG)

                    // Risk status section
                    if !viewModel.riskMessage.isEmpty {
                        VStack(spacing: BabyPulseLayout.spacingMD) {
                            ModernRiskBanner(
                                status: viewModel.riskStatus,
                                message: viewModel.riskMessage
                            )
                        }
                        .padding(.horizontal, BabyPulseLayout.paddingLG)
                    }

                    // Insights section
                    VStack(spacing: BabyPulseLayout.spacingMD) {
                        HStack {
                            sectionHeader("AI Insights", subtitle: "Personalized recommendations")

                            Spacer()

                            Button {
                                // Navigate to insights
                            } label: {
                                HStack(spacing: 4) {
                                    Text("See All")
                                        .font(BabyPulseTypography.callout())
                                        .fontWeight(.medium)

                                    Image(systemName: "chevron.right")
                                        .font(.system(size: 12, weight: .semibold))
                                }
                                .foregroundColor(BabyPulseColors.primary)
                            }
                        }
                        .padding(.horizontal, BabyPulseLayout.paddingLG)

                        if viewModel.insights.isEmpty {
                            modernEmptyInsightsView
                                .padding(.horizontal, BabyPulseLayout.paddingLG)
                        } else {
                            // Horizontal scrolling insights
                            ScrollView(.horizontal, showsIndicators: false) {
                                LazyHStack(spacing: BabyPulseLayout.spacingMD) {
                                    ForEach(viewModel.insights, id: \.id) { insight in
                                        ModernInsightCard(insight: insight) { insight in
                                            viewModel.showInsightDetail(insight: insight)
                                        }
                                        .frame(width: 300)
                                    }
                                }
                                .padding(.horizontal, BabyPulseLayout.paddingLG)
                            }
                        }
                    }

                    // Bottom spacing
                    Color.clear.frame(height: BabyPulseLayout.spacingLG)
                }
                .refreshable {
                    await refreshData()
                }
            }
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                scrollOffset = value
            }

            // Loading overlay
            if viewModel.isLoading {
                modernLoadingOverlay
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            viewModel.modelContext = modelContext
            viewModel.loadData()

            // Animate content appearance
            withAnimation(.easeOut(duration: 0.5)) {
                hasAppeared = true
            }
        }
        .alert(item: Binding<AlertItem?>(
            get: { viewModel.errorMessage != nil ? AlertItem(message: viewModel.errorMessage!) : nil },
            set: { _ in viewModel.errorMessage = nil }
        )) { alertItem in
            Alert(
                title: Text("Error"),
                message: Text(alertItem.message),
                dismissButton: .default(Text("OK"))
            )
        }
        .sheet(isPresented: $viewModel.showingInsightDetail) {
            if let insight = viewModel.selectedInsight {
                NavigationView {
                    InsightDetailView(insight: insight)
                }
            }
        }
        .sheet(isPresented: $showingLogEntrySheet) {
            if let category = selectedLogCategory {
                LogEntryView(category: category)
            }
        }
    }

    // MARK: - View Components

    private func sectionHeader(_ title: String, subtitle: String? = nil) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(BabyPulseTypography.title2())
                .fontWeight(.bold)
                .foregroundColor(BabyPulseColors.text)

            if let subtitle = subtitle {
                Text(subtitle)
                    .font(BabyPulseTypography.footnote())
                    .foregroundColor(BabyPulseColors.textSecondary)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    private var modernLoadingOverlay: some View {
        ZStack {
            Color.black.opacity(0.3)
                .ignoresSafeArea()
                .blur(radius: 2)

            VStack(spacing: BabyPulseLayout.spacingMD) {
                ProgressView()
                    .scaleEffect(1.5)
                    .progressViewStyle(CircularProgressViewStyle(tint: BabyPulseColors.primary))

                Text("Loading...")
                    .font(BabyPulseTypography.callout())
                    .foregroundColor(.white)
            }
            .padding(BabyPulseLayout.paddingXL)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                    .fill(.ultraThinMaterial)
            )
        }
        .transition(.opacity)
    }

    private var modernEmptyInsightsView: some View {
        VStack(spacing: BabyPulseLayout.spacingMD) {
            Image(systemName: "lightbulb")
                .font(.system(size: 48))
                .foregroundColor(BabyPulseColors.primary.opacity(0.6))

            Text("No insights yet")
                .font(BabyPulseTypography.title3())
                .fontWeight(.semibold)
                .foregroundColor(BabyPulseColors.text)

            Text("Log more activities to generate personalized insights for your baby.")
                .font(BabyPulseTypography.body())
                .foregroundColor(BabyPulseColors.textSecondary)
                .multilineTextAlignment(.center)
                .lineLimit(3)

            Button {
                // Navigate to logs
            } label: {
                Text("Log an Activity")
                    .font(BabyPulseTypography.callout())
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, BabyPulseLayout.paddingLG)
                    .padding(.vertical, BabyPulseLayout.paddingMD)
                    .background(
                        Capsule()
                            .fill(BabyPulseColors.primary)
                    )
            }
            .padding(.top, BabyPulseLayout.paddingSM)
        }
        .frame(maxWidth: .infinity)
        .padding(BabyPulseLayout.paddingXL)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .fill(BabyPulseColors.surfacePrimary)
                .applyShadow(BabyPulseShadows.large(colorScheme: colorScheme))
        )
    }

    // MARK: - Helper Functions

    private func formatBabyAge(birthDate: Date) -> String {
        let now = Date()
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.year, .month, .day], from: birthDate, to: now)

        if let years = ageComponents.year, years > 0 {
            if let months = ageComponents.month, months > 0 {
                return "\(years) year\(years == 1 ? "" : "s"), \(months) month\(months == 1 ? "" : "s")"
            } else {
                return "\(years) year\(years == 1 ? "" : "s")"
            }
        } else if let months = ageComponents.month, months > 0 {
            if let days = ageComponents.day, days > 0 {
                return "\(months) month\(months == 1 ? "" : "s"), \(days) day\(days == 1 ? "" : "s")"
            } else {
                return "\(months) month\(months == 1 ? "" : "s")"
            }
        } else if let days = ageComponents.day {
            return "\(days) day\(days == 1 ? "" : "s")"
        } else {
            return "Newborn"
        }
    }

    private func convertToDict(_ summary: DailySummary) -> [String: Any] {
        return [
            "feedingCount": summary.totalFeedings,
            "breastFeedings": summary.breastFeedings,
            "bottleFeedings": summary.bottleFeedings,
            "solidFeedings": summary.solidFeedings,
            "feedingVolume": summary.totalFeedingVolume,
            "diaperCount": summary.totalDiapers,
            "wetDiapers": summary.wetDiapers,
            "dirtyDiapers": summary.dirtyDiapers,
            "mixedDiapers": summary.mixedDiapers,
            "sleepMinutes": Int(summary.totalSleepHours * 60),
            "sleepSessions": summary.sleepSessions
        ]
    }

    private func refreshData() async {
        isRefreshing = true
        viewModel.loadData()
        try? await Task.sleep(nanoseconds: 1_000_000_000)
        isRefreshing = false
    }
}

// MARK: - Scroll Detection
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// MARK: - Modern Components

struct ModernBabyProfileHeader: View {
    let babyName: String
    let babyAge: String
    let profileImage: UIImage?
    let scrollOffset: CGFloat
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        VStack(spacing: BabyPulseLayout.spacingMD) {
            // Profile image with modern design
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                BabyPulseColors.primary.opacity(0.3),
                                BabyPulseColors.primary.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)

                if let image = profileImage {
                    Image(uiImage: image)
                        .resizable()
                        .scaledToFill()
                        .frame(width: 100, height: 100)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(
                                    LinearGradient(
                                        colors: [BabyPulseColors.primary, BabyPulseColors.primary.opacity(0.6)],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 3
                                )
                        )
                        .shadow(color: BabyPulseColors.primary.opacity(0.3), radius: 10, x: 0, y: 5)
                } else {
                    Image(systemName: "person.fill")
                        .font(.system(size: 50, weight: .medium))
                        .foregroundColor(BabyPulseColors.primary)
                }
            }
            .offset(y: min(0, scrollOffset * 0.1)) // Subtle parallax effect

            // Baby info with improved typography
            VStack(spacing: 6) {
                Text(babyName)
                    .font(BabyPulseTypography.title1())
                    .fontWeight(.bold)
                    .foregroundColor(BabyPulseColors.text)

                Text(babyAge)
                    .font(BabyPulseTypography.body())
                    .foregroundColor(BabyPulseColors.textSecondary)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, BabyPulseLayout.paddingLG)
    }
}

struct ModernTodaySummaryCard: View {
    let summaryData: [String: Any]
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        VStack(spacing: BabyPulseLayout.spacingLG) {
            // Header
            VStack(alignment: .leading, spacing: 4) {
                Text("Today's Summary")
                    .font(BabyPulseTypography.title3())
                    .fontWeight(.bold)
                    .foregroundColor(BabyPulseColors.text)

//                Text(Date(), style: .date)
//                    .font(BabyPulseTypography.footnote())
//                    .foregroundColor(BabyPulseColors.textSecondary)
            }
            // Summary grid
            LazyVGrid(
                columns: Array(repeating: GridItem(.flexible(), spacing: BabyPulseLayout.spacingMD), count: 3),
                spacing: BabyPulseLayout.spacingMD
            ) {
                SummaryStatCard(
                    icon: "drop.fill",
                    value: "\(summaryData["feedingCount"] as? Int ?? 0)",
                    color: BabyPulseColors.feeding
                )

                SummaryStatCard(
                    icon: "heart.fill",
                    value: "\(summaryData["diaperCount"] as? Int ?? 0)",
                    color: BabyPulseColors.diaper
                )

                SummaryStatCard(
                    icon: "moon.fill",
                    value: String(format: "%.1fh", Double(summaryData["sleepMinutes"] as? Int ?? 0) / 60.0),
                    color: BabyPulseColors.sleep
                )
            }
        }
        .padding(BabyPulseLayout.paddingLG)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .fill(colorScheme == .dark ? Color(hex: "1C1C1E") : Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
        )
    }
}

struct SummaryStatCard: View {
    let icon: String
    let value: String
    let color: Color
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        VStack(spacing: BabyPulseLayout.spacingSM - 4) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(color)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(color.opacity(0.15))
                )

            Text(value)
                .font(BabyPulseTypography.title3())
                .fontWeight(.bold)
                .foregroundColor(BabyPulseColors.text)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, BabyPulseLayout.paddingMD - 4)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusMD)
                .fill(BabyPulseColors.surfaceSecondary)
        )
    }
}

struct ModernRiskBanner: View {
    let status: RiskStatus
    let message: String
    @Environment(\.colorScheme) private var colorScheme

    private var bannerColor: Color {
        switch status {
        case .alert: return BabyPulseColors.error
        case .warning: return BabyPulseColors.warning
        default: return BabyPulseColors.info
        }
    }

    var body: some View {
        HStack(spacing: BabyPulseLayout.spacingMD) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(bannerColor)

            VStack(alignment: .leading, spacing: 4) {
                Text("Health Alert")
                    .font(BabyPulseTypography.callout())
                    .fontWeight(.semibold)
                    .foregroundColor(BabyPulseColors.text)

                Text(message)
                    .font(BabyPulseTypography.footnote())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .lineLimit(3)
            }

            Spacer()
        }
        .padding(BabyPulseLayout.paddingLG)
        .background(
            RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                .fill(bannerColor.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                        .stroke(bannerColor.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct ModernInsightCard: View {
    let insight: Insight
    let onTap: (Insight) -> Void
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        Button {
            onTap(insight)
        } label: {
            VStack(alignment: .leading, spacing: BabyPulseLayout.spacingMD) {
                HStack {
                    Image(systemName: "lightbulb.fill")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(BabyPulseColors.primary)

                    Spacer()

                    Text(insight.timestamp, style: .date)
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }

                Text(insight.title)
                    .font(BabyPulseTypography.callout())
                    .fontWeight(.semibold)
                    .foregroundColor(BabyPulseColors.text)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)

                Text(insight.insightContent)
                    .font(BabyPulseTypography.footnote())
                    .foregroundColor(BabyPulseColors.textSecondary)
                    .lineLimit(4)
                    .multilineTextAlignment(.leading)

                HStack {
                    Text("Read more")
                        .font(BabyPulseTypography.caption())
                        .fontWeight(.semibold)
                        .foregroundColor(BabyPulseColors.primary)

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(BabyPulseColors.primary)
                }
            }
            .padding(BabyPulseLayout.paddingLG)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                    .fill(BabyPulseColors.surfacePrimary)
                    .applyShadow(BabyPulseShadows.large(colorScheme: colorScheme))
            )
        }
        .buttonStyle(ScaleButtonStyle())
    }
}

struct ModernQuickActionButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let action: () -> Void
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        Button(action: action) {
            VStack(spacing: BabyPulseLayout.spacingMD) {
                HStack {
                    Image(systemName: icon)
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(color)

                    Spacer()

                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(color.opacity(0.6))
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(BabyPulseTypography.callout())
                        .fontWeight(.semibold)
                        .foregroundColor(BabyPulseColors.text)

                    Text(subtitle)
                        .font(BabyPulseTypography.caption())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(BabyPulseLayout.paddingLG)
            .frame(height: 100)
            .background(
                RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                    .fill(BabyPulseColors.surfacePrimary)
                    .overlay(
                        RoundedRectangle(cornerRadius: BabyPulseLayout.cornerRadiusLG)
                            .stroke(color.opacity(0.2), lineWidth: 1)
                    )
                    .applyShadow(BabyPulseShadows.medium(colorScheme: colorScheme))
            )
        }
        .buttonStyle(ScaleButtonStyle())
    }
}

struct SummaryItemView: View {
    let icon: String
    let value: Int
    var unit: String = ""
    let label: String
    let color: Color

    // For double values
    init(icon: String, value: Double, unit: String = "", label: String, color: Color) {
        self.icon = icon
        self.value = 0 // Not used for double values
        self.unit = unit
        self.label = label
        self.color = color
        self._doubleValue = State(initialValue: value)
    }

    // For integer values
    init(icon: String, value: Int, unit: String = "", label: String, color: Color) {
        self.icon = icon
        self.value = value
        self.unit = unit
        self.label = label
        self.color = color
        self._doubleValue = State(initialValue: 0) // Not used for int values
    }

    @State private var doubleValue: Double

    var body: some View {
        VStack(spacing: 4) { // Reduced spacing
            // Icon in circle
            ZStack {
                Circle()
                    .fill(color.opacity(0.15))
                    .frame(width: 44, height: 44) // Slightly smaller icon background

                Image(systemName: icon)
                    .font(.system(size: 18)) // Slightly smaller icon
                    .foregroundColor(color)
            }

            // Value display with dynamic formatting
            Group {
                if doubleValue > 0 {
                    HStack(alignment: .lastTextBaseline, spacing: 2) {
                        Text(String(format: "%.1f", doubleValue))
                            .font(BabyPulseTypography.title3()) // Keep value font size

                        if !unit.isEmpty {
                            Text(unit)
                                .font(BabyPulseTypography.footnote())
                                .foregroundColor(BabyPulseColors.textSecondary)
                        }
                    }
                } else {
                    HStack(alignment: .lastTextBaseline, spacing: 2) {
                        Text("\(value)")
                            .font(BabyPulseTypography.title3()) // Keep value font size

                        if !unit.isEmpty {
                            Text(unit)
                                .font(BabyPulseTypography.footnote())
                                .foregroundColor(BabyPulseColors.textSecondary)
                        }
                    }
                }
            }
            .foregroundColor(BabyPulseColors.secondary)

            // Text(label) // Label removed as requested
            //     .font(BabyPulseTypography.caption())
            //     .foregroundColor(BabyPulseColors.textSecondary)
        }
        .frame(maxWidth: .infinity)
        // .padding(.vertical, 8) // Add some vertical padding if needed after label removal
    }
}

// MARK: - Supporting Types
struct AlertItem: Identifiable {
    let id = UUID()
    let message: String
}

#Preview {
    HomeView()
        .modelContainer(for: [Baby.self, UserPreferences.self, FeedingEntry.self, DiaperEntry.self, SleepEntry.self, GrowthEntry.self, HealthEntry.self, ChatMessage.self, ChatThread.self, Insight.self], inMemory: true)
}
