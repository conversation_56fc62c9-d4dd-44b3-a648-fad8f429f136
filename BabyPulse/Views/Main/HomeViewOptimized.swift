import SwiftUI
import ComposableArchitecture

struct HomeViewOptimized: View {
    @Bindable var store: StoreOf<HomeFeature>
    
    var body: some View {
        NavigationStack {
            ScrollView {
                LazyVStack(spacing: 20) {
                    if store.isLoading {
                        LoadingSection()
                    } else if let errorMessage = store.errorMessage {
                        ErrorSection(message: errorMessage) {
                            store.send(.refreshData)
                        }
                    } else {
                        MainContent(store: store)
                    }
                }
                .padding(.horizontal)
            }
            .navigationTitle("Home")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                store.send(.refreshData)
            }
            .onAppear {
                store.send(.onAppear)
            }
        }
        .trackPerformance(viewName: "HomeView")
        .sheet(isPresented: $store.showingInsightDetail) {
            if let insight = store.selectedInsight {
                InsightDetailView(insight: insight) {
                    store.send(.dismissInsightDetail)
                }
            }
        }
    }
}

// MARK: - Main Content
private struct MainContent: View {
    @Bindable var store: StoreOf<HomeFeature>
    
    var body: some View {
        VStack(spacing: 24) {
            if store.hasCurrentBaby {
                BabyHeaderSection(baby: store.currentBaby!)
                RiskStatusSection(
                    status: store.riskStatus,
                    message: store.riskMessage
                )
                DailySummarySection(cards: store.summaryCards)
                RecentInsightsSection(
                    insights: store.insights,
                    onInsightTap: { insight in
                        store.send(.insightSelected(insight))
                    }
                )
            } else {
                NoBabySection()
            }
        }
    }
}

// MARK: - Baby Header Section
private struct BabyHeaderSection: View {
    let baby: Baby
    
    var body: some View {
        VStack(spacing: 12) {
            AsyncImage(url: baby.photoURL) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.gray.opacity(0.3))
            }
            .frame(width: 80, height: 80)
            .clipShape(Circle())
            .overlay(
                Circle()
                    .stroke(Color.blue.opacity(0.3), lineWidth: 3)
            )
            
            VStack(spacing: 4) {
                Text(baby.name)
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("\(baby.ageInDays) days old")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 8)
    }
}

// MARK: - Risk Status Section
private struct RiskStatusSection: View {
    let status: RiskStatus
    let message: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: status.icon)
                .font(.title2)
                .foregroundColor(status.color)
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Health Status")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .textCase(.uppercase)
                
                Text(message)
                    .font(.subheadline)
                    .multilineTextAlignment(.leading)
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(status.color.opacity(0.1))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(status.color.opacity(0.3), lineWidth: 1)
        )
    }
}

// MARK: - Daily Summary Section
private struct DailySummarySection: View {
    let cards: [SummaryCard]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Today's Summary")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("Last 24 hours")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(cards) { card in
                    SummaryCardView(card: card)
                }
            }
        }
    }
}

// MARK: - Summary Card View
private struct SummaryCardView: View {
    let card: SummaryCard
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: card.icon)
                .font(.title2)
                .foregroundColor(card.color)
            
            Text(card.value)
                .font(.title3)
                .fontWeight(.bold)
            
            Text(card.subtitle)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray5), lineWidth: 1)
        )
    }
}

// MARK: - Recent Insights Section
private struct RecentInsightsSection: View {
    let insights: [Insight]
    let onInsightTap: (Insight) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Recent Insights")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if !insights.isEmpty {
                    NavigationLink("View All") {
                        // Navigate to insights view
                    }
                    .font(.subheadline)
                    .foregroundColor(.blue)
                }
            }
            
            if insights.isEmpty {
                EmptyInsightsView()
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(insights.prefix(3), id: \.id) { insight in
                        InsightRowView(insight: insight) {
                            onInsightTap(insight)
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Insight Row View
private struct InsightRowView: View {
    let insight: Insight
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                VStack {
                    Image(systemName: insight.category.icon)
                        .font(.title3)
                        .foregroundColor(insight.severity.color)
                    
                    if insight.needsAttention {
                        Circle()
                            .fill(Color.red)
                            .frame(width: 8, height: 8)
                    }
                }
                .frame(width: 40)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(insight.title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .multilineTextAlignment(.leading)
                    
                    Text(insight.summary)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                    
                    HStack {
                        Text(insight.timestamp.formatted(.relative(presentation: .named)))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        if insight.confidence > 0.8 {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.caption)
                                .foregroundColor(.green)
                        }
                    }
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(.systemGray6), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Empty States
private struct EmptyInsightsView: View {
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "lightbulb")
                .font(.title)
                .foregroundColor(.gray.opacity(0.6))
            
            Text("No insights yet")
                .font(.subheadline)
                .fontWeight(.medium)
            
            Text("Keep logging activities to get personalized insights about your baby's patterns.")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 24)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6).opacity(0.5))
        )
    }
}

private struct NoBabySection: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "person.badge.plus")
                .font(.system(size: 60))
                .foregroundColor(.blue.opacity(0.6))
            
            VStack(spacing: 8) {
                Text("Welcome to BabyPulse")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Add your baby's profile to start tracking their activities and get personalized insights.")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            NavigationLink("Add Baby Profile") {
                // Navigate to baby profile creation
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .padding(.vertical, 40)
    }
}

// MARK: - Loading and Error States
private struct LoadingSection: View {
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Loading your baby's data...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 40)
    }
}

private struct ErrorSection: View {
    let message: String
    let onRetry: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.title)
                .foregroundColor(.orange)
            
            Text("Something went wrong")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Try Again", action: onRetry)
                .buttonStyle(.borderedProminent)
        }
        .padding(.vertical, 40)
    }
}

// MARK: - Insight Detail View
private struct InsightDetailView: View {
    let insight: Insight
    let onDismiss: () -> Void
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Insight header
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Image(systemName: insight.category.icon)
                                .font(.title2)
                                .foregroundColor(insight.severity.color)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text(insight.category.displayName)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .textCase(.uppercase)
                                
                                Text(insight.title)
                                    .font(.title3)
                                    .fontWeight(.semibold)
                            }
                            
                            Spacer()
                            
                            if insight.needsAttention {
                                Image(systemName: "exclamationmark.circle.fill")
                                    .foregroundColor(.red)
                            }
                        }
                        
                        Text(insight.timestamp.formatted(.dateTime.weekday().month().day().hour().minute()))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(insight.severity.color.opacity(0.1))
                    )
                    
                    // Insight content
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Summary")
                            .font(.headline)
                        
                        Text(insight.summary)
                            .font(.body)
                        
                        if !insight.content.isEmpty {
                            Text("Details")
                                .font(.headline)
                            
                            Text(insight.content)
                                .font(.body)
                        }
                        
                        // Confidence indicator
                        HStack {
                            Text("Confidence")
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            Spacer()
                            
                            ProgressView(value: insight.confidence)
                                .frame(width: 100)
                            
                            Text("\(Int(insight.confidence * 100))%")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color(.systemGray6))
                        )
                    }
                    .padding()
                }
            }
            .navigationTitle("Insight")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done", action: onDismiss)
                }
            }
        }
    }
}

// MARK: - Extensions

#Preview {
    HomeViewOptimized(
        store: Store(initialState: HomeFeature.State()) {
            HomeFeature()
        }
    )
} 