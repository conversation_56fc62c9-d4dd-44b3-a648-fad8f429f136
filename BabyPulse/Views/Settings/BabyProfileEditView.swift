//
//  BabyProfileEditView.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import SwiftUI
import PhotosUI

struct BabyProfileEditView: View {
    @ObservedObject var viewModel: SettingsViewModel
    let isEditing: Bool
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        Form {
            Section {
                photoSelectionView
                nameFieldView
                birthDatePicker
                genderPicker
            } header: {
                Text("Baby Information")
            } footer: {
                if !isEditing && !viewModel.babies.isEmpty {
                    Text("Note: Full multi-baby support is coming in the next version. Adding multiple babies may have limited functionality.")
                        .font(BabyPulseTypography.footnote())
                        .foregroundColor(BabyPulseColors.textSecondary)
                }
            }
        }
        .navigationTitle(isEditing ? "Edit Baby Profile" : "Add Baby Profile")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                But<PERSON>("Cancel") {
                    dismiss()
                }
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                But<PERSON>("Save") {
                    viewModel.saveBabyProfile()
                    if viewModel.nameError == nil {
                        dismiss()
                    }
                }
                .bold()
                .foregroundColor(BabyPulseColors.primary)
            }
        }
    }

    // MARK: - Computed Properties

    private var photoSelectionView: some View {
        HStack {
            Spacer()

            // PhotosPicker temporarily disabled for compilation
            // TODO: Re-enable when PhotosUI import issues are resolved
            Button(action: {
                // Placeholder action for photo selection
            }) {
                ZStack {
                    Circle()
                        .fill(BabyPulseColors.lightGray)
                        .frame(width: 100, height: 100)

                    if let photoData = viewModel.tempPhotoData, let uiImage = UIImage(data: photoData) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .scaledToFill()
                            .frame(width: 100, height: 100)
                            .clipShape(Circle())
                    } else {
                        Image(systemName: "person.fill")
                            .font(.system(size: 40))
                            .foregroundColor(BabyPulseColors.secondary)
                    }

                    Circle()
                        .stroke(BabyPulseColors.primary, lineWidth: 2)
                        .frame(width: 100, height: 100)

                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Image(systemName: "camera.fill")
                                .foregroundColor(.white)
                                .padding(8)
                                .background(Circle().fill(BabyPulseColors.primary))
                        }
                    }
                    .padding(8)
                }
                .frame(width: 100, height: 100)
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()
        }
        .padding(.vertical, 8)
    }

    private var nameFieldView: some View {
        VStack(alignment: .leading, spacing: 4) {
            TextField("Baby's Name", text: $viewModel.tempBabyName)
                .font(BabyPulseTypography.body())
                .padding(.vertical, 8)

            if let nameError = viewModel.nameError {
                Text(nameError)
                    .font(BabyPulseTypography.caption())
                    .foregroundColor(BabyPulseColors.error)
            }
        }
    }

    private var birthDatePicker: some View {
        DatePicker(
            "Birth Date",
            selection: $viewModel.tempBabyBirthDate,
            in: ...Date(),
            displayedComponents: .date
        )
        .datePickerStyle(.compact)
        .padding(.vertical, 8)
    }

    private var genderPicker: some View {
        Picker("Gender", selection: $viewModel.tempBabyGender) {
            Text("Male").tag(Gender.male)
            Text("Female").tag(Gender.female)
            Text("Other").tag(Gender.other)
        }
        .pickerStyle(.segmented)
        .padding(.vertical, 8)
    }
}

#Preview {
    NavigationStack {
        BabyProfileEditView(viewModel: SettingsViewModel(), isEditing: false)
    }
}
