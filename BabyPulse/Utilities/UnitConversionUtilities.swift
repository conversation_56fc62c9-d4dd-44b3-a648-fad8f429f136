//
//  UnitConversionUtilities.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftUI

/// Utility for converting between metric and imperial units and formatting measurements
struct UnitConverter {

    // MARK: - Weight Conversion

    /// Convert weight from kilograms to pounds
    /// - Parameter kg: Weight in kilograms
    /// - Returns: Weight in pounds
    static func kgToPounds(_ kg: Double) -> Double {
        return kg * 2.20462
    }

    /// Convert weight from pounds to kilograms
    /// - Parameter pounds: Weight in pounds
    /// - Returns: Weight in kilograms
    static func poundsToKg(_ pounds: Double) -> Double {
        return pounds / 2.20462
    }

    // MARK: - Length/Height Conversion

    /// Convert length from centimeters to inches
    /// - Parameter cm: Length in centimeters
    /// - Returns: Length in inches
    static func cmToInches(_ cm: Double) -> Double {
        return cm * 0.393701
    }

    /// Convert length from inches to centimeters
    /// - Parameter inches: Length in inches
    /// - Returns: Length in centimeters
    static func inchesToCm(_ inches: Double) -> Double {
        return inches / 0.393701
    }

    // MARK: - Temperature Conversion

    /// Convert temperature from Celsius to Fahrenheit
    /// - Parameter celsius: Temperature in Celsius
    /// - Returns: Temperature in Fahrenheit
    static func celsiusToFahrenheit(_ celsius: Double) -> Double {
        return (celsius * 9/5) + 32
    }

    /// Convert temperature from Fahrenheit to Celsius
    /// - Parameter fahrenheit: Temperature in Fahrenheit
    /// - Returns: Temperature in Celsius
    static func fahrenheitToCelsius(_ fahrenheit: Double) -> Double {
        return (fahrenheit - 32) * 5/9
    }

    // MARK: - Formatting

    /// Format weight according to the specified unit system
    /// - Parameters:
    ///   - weight: Weight in kilograms (the canonical storage format)
    ///   - unitSystem: The unit system to format for
    /// - Returns: Formatted weight string with appropriate unit
    static func formatWeight(_ weight: Double, unitSystem: UnitSystem) -> String {
        switch unitSystem {
        case .metric:
            return String(format: "%.2f kg", weight)
        case .imperial:
            let pounds = kgToPounds(weight)
            return String(format: "%.2f lb", pounds)
        }
    }

    /// Format height/length according to the specified unit system
    /// - Parameters:
    ///   - height: Height in centimeters (the canonical storage format)
    ///   - unitSystem: The unit system to format for
    /// - Returns: Formatted height string with appropriate unit
    static func formatHeight(_ height: Double, unitSystem: UnitSystem) -> String {
        switch unitSystem {
        case .metric:
            return String(format: "%.1f cm", height)
        case .imperial:
            let inches = cmToInches(height)
            return String(format: "%.1f in", inches)
        }
    }

    /// Format head circumference according to the specified unit system
    /// - Parameters:
    ///   - circumference: Head circumference in centimeters (the canonical storage format)
    ///   - unitSystem: The unit system to format for
    /// - Returns: Formatted head circumference string with appropriate unit
    static func formatHeadCircumference(_ circumference: Double, unitSystem: UnitSystem) -> String {
        switch unitSystem {
        case .metric:
            return String(format: "%.1f cm", circumference)
        case .imperial:
            let inches = cmToInches(circumference)
            return String(format: "%.1f in", inches)
        }
    }

    /// Format temperature with the appropriate unit
    /// - Parameters:
    ///   - temperature: Temperature value
    ///   - unit: The temperature unit (celsius or fahrenheit)
    ///   - convertTo: Optional unit system to convert to
    /// - Returns: Formatted temperature string with appropriate unit
    static func formatTemperature(_ temperature: Double, unit: HealthEntry.TemperatureUnit, convertTo unitSystem: UnitSystem? = nil) -> String {
        // If no conversion requested, just format with the original unit
        if unitSystem == nil {
            return String(format: "%.1f%@", temperature, unit.description)
        }

        // Convert if needed
        switch unitSystem! {
        case .metric:
            if unit == .celsius {
                return String(format: "%.1f°C", temperature)
            } else {
                let celsius = fahrenheitToCelsius(temperature)
                return String(format: "%.1f°C", celsius)
            }
        case .imperial:
            if unit == .fahrenheit {
                return String(format: "%.1f°F", temperature)
            } else {
                let fahrenheit = celsiusToFahrenheit(temperature)
                return String(format: "%.1f°F", fahrenheit)
            }
        }
    }

    /// Get the appropriate temperature unit for a unit system
    /// - Parameter unitSystem: The unit system
    /// - Returns: The corresponding temperature unit
    static func temperatureUnitFor(unitSystem: UnitSystem) -> HealthEntry.TemperatureUnit {
        switch unitSystem {
        case .metric:
            return .celsius
        case .imperial:
            return .fahrenheit
        }
    }
}

// MARK: - Extensions

/// Extension to provide unit conversion access via environment values
extension EnvironmentValues {
    private struct UnitSystemKey: EnvironmentKey {
        static let defaultValue: UnitSystem = .metric
    }

    /// The current unit system preference
    var unitSystem: UnitSystem {
        get { self[UnitSystemKey.self] }
        set { self[UnitSystemKey.self] = newValue }
    }
}

/// Extension to provide easy access to unit system from views
extension View {
    /// Set the unit system for this view and its child views
    /// - Parameter unitSystem: The unit system to use
    /// - Returns: A view with the environment value set
    func unitSystem(_ unitSystem: UnitSystem) -> some View {
        environment(\.unitSystem, unitSystem)
    }
}
