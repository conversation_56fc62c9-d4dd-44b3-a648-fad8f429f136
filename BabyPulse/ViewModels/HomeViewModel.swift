//
//  HomeViewModel.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData
import Combine

class HomeViewModel: ObservableObject {
    @Published var currentBaby: Baby?
    @Published var todaySummary: DailySummary = DailySummary()
    @Published var riskStatus: RiskStatus = .normal
    @Published var riskMessage: String = "Everything looks good!"
    @Published var insights: [Insight] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var selectedInsight: Insight?
    @Published var showingInsightDetail: Bool = false

    var modelContext: ModelContext?
    private var insightService: EnhancedInsightService?
    private var insightObserver: AnyCancellable?

    init(modelContext: ModelContext? = nil) {
        self.modelContext = modelContext
        loadData()
        setupInsightObserver()
    }

    private func setupInsightObserver() {
        // Observe for new insights
        insightObserver = NotificationCenter.default.publisher(for: .insightsGenerated)
            .sink { [weak self] notification in
                guard let self = self,
                      let newInsights = notification.object as? [Insight] else {
                    return
                }

                // Update insights on the main thread
                DispatchQueue.main.async {
                    // Add new insights to the top of the list
                    self.insights = (newInsights + self.insights).prefix(10).map { $0 }
                }
            }
    }

    func loadData() {
        isLoading = true
        errorMessage = nil

        Task {
            await loadCurrentBaby()
            await loadTodaySummary()
            await assessRisk()
            // Removed automatic insights generation
            // Insights will only be generated manually now

            await MainActor.run {
                isLoading = false
            }
        }
    }

    func showInsightDetail(insight: Insight) {
        selectedInsight = insight
        showingInsightDetail = true
    }

    @MainActor
    private func loadCurrentBaby() async {
        guard let modelContext = modelContext else {
            errorMessage = "Model context not available"
            return
        }

        do {
            // Use our safe method to fetch the selected baby
            if let baby = modelContext.fetchSelectedBaby() {
                currentBaby = baby
            } else {
                // No selected baby or it's invalid
                currentBaby = nil

                // Check if we have any babies at all
                let babyDescriptor = FetchDescriptor<Baby>()
                let babies = try modelContext.fetch(babyDescriptor)

                if let firstBaby = babies.first {
                    // If we have at least one baby, select it
                    currentBaby = firstBaby
                    modelContext.updateSelectedBabyID(firstBaby.id)
                }
            }
        } catch {
            errorMessage = "Failed to load baby profile: \(error.localizedDescription)"
        }
    }

    @MainActor
    private func loadTodaySummary() async {
        guard let modelContext = modelContext, currentBaby != nil else { return }

        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: today)!

        do {
            // Fetch feeding entries
            let feedingDescriptor = FetchDescriptor<FeedingEntry>(predicate: #Predicate { entry in
                entry.timestamp >= today && entry.timestamp < tomorrow
            })
            let feedings = try modelContext.fetch(feedingDescriptor)

            // Fetch diaper entries
            let diaperDescriptor = FetchDescriptor<DiaperEntry>(predicate: #Predicate { entry in
                entry.timestamp >= today && entry.timestamp < tomorrow
            })
            let diapers = try modelContext.fetch(diaperDescriptor)

            // Fetch sleep entries
            let sleepDescriptor = FetchDescriptor<SleepEntry>(predicate: #Predicate { entry in
                entry.timestamp >= today && entry.timestamp < tomorrow
            })
            let sleeps = try modelContext.fetch(sleepDescriptor)

            // Calculate summary
            todaySummary = calculateDailySummary(feedings: feedings, diapers: diapers, sleeps: sleeps)
        } catch {
            errorMessage = "Failed to load today's summary: \(error.localizedDescription)"
        }
    }

    private func calculateDailySummary(feedings: [FeedingEntry], diapers: [DiaperEntry], sleeps: [SleepEntry]) -> DailySummary {
        var summary = DailySummary()

        // Calculate feeding metrics
        summary.totalFeedings = feedings.count

        let breastFeedings = feedings.filter { $0.type == .breastfeeding }
        let bottleFeedings = feedings.filter { $0.type == .bottleFeeding }
        let solidFeedings = feedings.filter { $0.type == .solidFood }

        summary.breastFeedings = breastFeedings.count
        summary.bottleFeedings = bottleFeedings.count
        summary.solidFeedings = solidFeedings.count

        // Calculate total volume for bottle feedings
        summary.totalFeedingVolume = bottleFeedings.compactMap { $0.volume }.reduce(0, +)

        // Calculate diaper metrics
        summary.totalDiapers = diapers.count
        summary.wetDiapers = diapers.filter { $0.type == .wet }.count
        summary.dirtyDiapers = diapers.filter { $0.type == .dirty }.count
        summary.mixedDiapers = diapers.filter { $0.type == .mixed }.count

        // Calculate sleep metrics
        var totalSleepMinutes = 0
        for sleep in sleeps {
            if sleep.endTime == nil {
                // For ongoing sleep, calculate duration until now
                let now = Date()
                let duration = Int(now.timeIntervalSince(sleep.timestamp) / 60)
                totalSleepMinutes += duration
            } else if let endTime = sleep.endTime {
                // For completed sleep, use the recorded duration
                let duration = Int(endTime.timeIntervalSince(sleep.timestamp) / 60)
                totalSleepMinutes += duration
            }
        }

        summary.totalSleepHours = Double(totalSleepMinutes) / 60.0
        summary.sleepSessions = sleeps.count

        return summary
    }

    @MainActor
    private func assessRisk() async {
        // Simple risk assessment based on daily metrics
        if todaySummary.totalDiapers < 4 && currentBaby?.ageInDays ?? 0 < 30 {
            riskStatus = .warning
            riskMessage = "Low diaper count today. Newborns typically have 6-8 wet diapers per day."
        } else if todaySummary.totalFeedings < 5 && currentBaby?.ageInDays ?? 0 < 90 {
            riskStatus = .warning
            riskMessage = "Low feeding count today. Young babies typically feed 8-12 times per day."
        } else if todaySummary.totalSleepHours < 10 && currentBaby?.ageInDays ?? 0 < 180 {
            riskStatus = .warning
            riskMessage = "Low sleep hours today. Babies typically need 14-17 hours of sleep per day."
        } else {
            riskStatus = .normal
            riskMessage = "Everything looks good! Your baby's patterns are within normal ranges."
        }
    }

    @MainActor
    private func generateInsights() async {
        guard let baby = currentBaby, let modelContext = modelContext else {
            return
        }

        // Verify the baby is still valid before proceeding
        guard baby.isValid else {
            // Baby has been invalidated, reload current baby
            await loadCurrentBaby()
            return
        }

        // Initialize insight service if needed
        if insightService == nil {
            insightService = EnhancedInsightService(modelContext: modelContext)
        }

        // Generate real insights using the enhanced service
        insightService?.generateInsights(for: baby) { [weak self] newInsights in
            Task { @MainActor in
                guard let self = self else { return }

                // Verify the baby is still valid
                guard baby.isValid else {
                    // Baby has been invalidated, reload data
                    self.loadData()
                    return
                }

                // If we got insights, use them
                if !newInsights.isEmpty {
                    self.insights = newInsights.sorted(by: { $0.timestamp > $1.timestamp }).prefix(3).map { $0 }
                } else {
                    // If no insights were generated, create some default ones
                    let defaultInsights = [
                        Insight(
                            id: UUID(),
                            category: .feeding,
                            title: "Feeding Pattern",
                            metric: "6-8 feedings per day",
                            insightContent: "Your baby is at an age where 6-8 feedings per day is typical. Make sure to watch for hunger cues and feed on demand.",
                            timestamp: Date(),
                            isRead: false,
                            needsAttention: false,
                            confidence: 90,
                            baby: baby
                        ),
                        Insight(
                            id: UUID(),
                            category: .sleep,
                            title: "Sleep Duration",
                            metric: "14-16 hours daily",
                            insightContent: "Babies at this age typically sleep 14-16 hours in a 24-hour period. Establishing a bedtime routine can help improve sleep quality.",
                            timestamp: Date(),
                            isRead: false,
                            needsAttention: true,
                            confidence: 85,
                            baby: baby
                        ),
                        Insight(
                            id: UUID(),
                            category: .diaper,
                            title: "Diaper Changes",
                            metric: "6-8 changes daily",
                            insightContent: "Expect 6-8 wet diapers per day as an indicator of good hydration. The color and consistency of stool can vary based on diet.",
                            timestamp: Date(),
                            isRead: false,
                            needsAttention: false,
                            confidence: 95,
                            baby: baby
                        )
                    ]
                    self.insights = defaultInsights
                }
            }
        }
    }


}

// MARK: - Supporting Models
// Models moved to BabyPulse/Models/DailySummary.swift to avoid duplication
