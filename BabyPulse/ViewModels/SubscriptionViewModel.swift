import Foundation
import StoreKit
import Combine

class SubscriptionViewModel: ObservableObject {
    // MARK: - Properties

    // Services
    private let revenueCatService = RevenueCatService.shared

    // Products
    @Published var products: [Product] = []
    @Published var selectedProductId: String?

    // Subscription status
    @Published var subscriptionStatus: RevenueCatService.SubscriptionStatus = .free

    // Loading state
    @Published var isLoading = false
    @Published var errorMessage: String?

    // Success state
    @Published var purchaseSuccess = false

    // Cancellables
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization

    init() {
        // Subscribe to products
        revenueCatService.$products
            .sink { [weak self] products in
                self?.products = products
            }
            .store(in: &cancellables)

        // Subscribe to subscription status
        revenueCatService.$subscriptionStatus
            .sink { [weak self] status in
                self?.subscriptionStatus = status
            }
            .store(in: &cancellables)

        // Subscribe to loading state
        revenueCatService.$isLoading
            .sink { [weak self] isLoading in
                self?.isLoading = isLoading
            }
            .store(in: &cancellables)

        // Subscribe to error message
        revenueCatService.$errorMessage
            .sink { [weak self] errorMessage in
                self?.errorMessage = errorMessage
            }
            .store(in: &cancellables)

        // Load products
        Task {
            await loadProducts()
        }
    }

    // MARK: - Methods

    /// Load available products
    @MainActor
    func loadProducts() async {
        await revenueCatService.loadProducts()

        // Set default selected product to monthly
        if let monthlyProduct = products.first(where: { $0.id == RevenueCatConfig.ProductIDs.monthly }) {
            selectedProductId = monthlyProduct.id
        }
    }

    /// Purchase selected product
    @MainActor
    func purchaseSelectedProduct() async -> Bool {
        guard let productId = selectedProductId else {
            errorMessage = "No product selected"
            return false
        }

        let success = await revenueCatService.purchase(productId: productId)

        if success {
            purchaseSuccess = true
        }

        return success
    }

    /// Restore purchases
    @MainActor
    func restorePurchases() async -> Bool {
        let success = await revenueCatService.restorePurchases()

        if success && revenueCatService.hasPremiumAccess() {
            purchaseSuccess = true
        }

        return success
    }

    /// Format price for display
    func formattedPrice(for product: Product) -> String {
        // Use the product's displayPrice directly
        let price = product.displayPrice

        // Add subscription period if applicable
        if let subscription = product.subscription {
            switch subscription.subscriptionPeriod.unit {
            case .day:
                return "\(price)/day"
            case .week:
                return "\(price)/week"
            case .month:
                return "\(price)/month"
            case .year:
                return "\(price)/year"
            @unknown default:
                return price
            }
        }

        return price
    }

    /// Check if user has premium access
    func hasPremiumAccess() -> Bool {
        return revenueCatService.hasPremiumAccess()
    }
}
