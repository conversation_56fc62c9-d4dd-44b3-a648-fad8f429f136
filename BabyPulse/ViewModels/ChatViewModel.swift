import Foundation
import SwiftData
import Combine
import OSLog

@preconcurrency import class SwiftData.ModelContext

class ChatViewModel: ObservableObject {
    @Published var messages: [ChatMessage] = []
    @Published var inputText: String = ""
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var suggestedQuestions: [String] = []
    @Published var isStreaming: Bool = false

    private(set) var thread: ChatThread?
    private var modelContext: ModelContext?
    private var llmService: LLMService
    private var cancellables = Set<AnyCancellable>()
    private var supabaseService = SupabaseService.shared

    init(thread: ChatThread? = nil) {
        self.thread = thread
        self.llmService = LLMService()

        // Generate initial suggested questions
        generateSuggestedQuestions()
    }

    func setModelContext(_ context: ModelContext) {
        self.modelContext = context
        // Regenerate suggested questions now that we have model context
        generateSuggestedQuestions()
    }

    func loadMessages() {
        guard let modelContext = modelContext else {
            errorMessage = "Model context not available"
            print("Error: Model context not available when loading messages")
            return
        }

        do {
            if let thread = thread {
                // Load messages for this thread
                // First fetch all messages
                let descriptor = FetchDescriptor<ChatMessage>(
                    sortBy: [SortDescriptor(\.timestamp, order: .forward)]
                )

                let allMessages = try modelContext.fetch(descriptor)

                // Then filter manually for the current thread
                let threadId = thread.id
                let loadedMessages = allMessages.filter { message in
                    message.thread?.id == threadId
                }

                // Update the messages array on the main thread
                DispatchQueue.main.async {
                    self.messages = loadedMessages
                    print("Loaded \(loadedMessages.count) messages for thread \(thread.id)")
                    
                    // Regenerate suggested questions after loading messages
                    self.generateSuggestedQuestions()
                }
            } else {
                // Create a new thread if none exists
                createNewThread()
            }
        } catch {
            errorMessage = "Failed to load messages: \(error.localizedDescription)"
            print("Error loading messages: \(error.localizedDescription)")
        }
    }

    private func createNewThread() {
        guard let modelContext = modelContext else { return }

        // Get the first baby
        do {
            let descriptor = FetchDescriptor<Baby>(sortBy: [SortDescriptor(\.name)])
            let babies = try modelContext.fetch(descriptor)

            if let firstBaby = babies.first {
                // Create a new thread
                let newThread = ChatThread(baby: firstBaby)
                modelContext.insert(newThread)

                // Add a welcome message
                let welcomeMessage = ChatMessage.createAssistantMessage(
                    content: "Hello! I'm your BabyPulse assistant. I can help answer questions about \(firstBaby.name)'s patterns and development. How can I help you today?",
                    thread: newThread
                )
                modelContext.insert(welcomeMessage)

                try self.modelContext?.save()

                // Set the thread and load messages
                self.thread = newThread
                self.messages = [welcomeMessage]
            }
        } catch {
            print("Error creating new thread: \(error)")
        }
    }

    func sendMessage() {
        // Validate input text is not empty
        let trimmedText = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedText.isEmpty else { return }

        // Create thread if needed
        if thread == nil {
            createNewThread()
        }

        guard let thread = thread, let baby = thread.baby else {
            errorMessage = "No active thread or baby"
            return
        }

        // Create and add user message
        let userMessage = ChatMessage.createUserMessage(
            content: trimmedText,
            thread: thread
        )
        print("Sending user message: \(trimmedText)")

        // Store the message text and clear the input field
        let userMessageText = trimmedText

        // Clear input field on main thread
        DispatchQueue.main.async {
            self.inputText = ""
        }

        // Add the message to the database and UI
        addMessage(userMessage)

        // Get baby data for context
        let babyData = getBabyDataContext(for: baby)

        // Set loading state
        DispatchQueue.main.async {
            self.isLoading = true
            self.errorMessage = nil
        }

        // Try to use Supabase chat function if available
        Task {
            do {
                let threadId = thread.id.uuidString
                let response = try await supabaseService.sendChatMessage(
                    babyId: baby.id.uuidString,
                    message: userMessageText,
                    threadId: threadId
                )

                DispatchQueue.main.async {
                    self.isLoading = false
                    print("Received response from Supabase")

                    let assistantMessage = ChatMessage.createAssistantMessage(
                        content: response.message,
                        tokenCount: response.message.count / 4, // Rough estimate
                        thread: self.thread
                    )
                    self.addMessage(assistantMessage)

                    // Generate new suggested questions after receiving a response
                    self.generateSuggestedQuestions()
                }
            } catch {
                print("Supabase chat error: \(error). Falling back to local LLM with streaming.")

                // Fall back to local LLM with streaming
                await handleStreamingResponse(userMessage: userMessageText, babyData: babyData, thread: thread)
            }
        }
    }

    /// Handle streaming response from LLM
    private func handleStreamingResponse(userMessage: String, babyData: String, thread: ChatThread) async {
        // Create a streaming placeholder message
        let streamingMessage = ChatMessage.createStreamingAssistantMessage(thread: thread)

        // Add the streaming message to the UI
        await MainActor.run {
            self.isLoading = false
            self.isStreaming = true
            self.addMessage(streamingMessage)
        }

        do {
            // Get the streaming response with a 45-second timeout for chat
            let stream = llmService.generateStreamingResponse(userMessage: userMessage, babyData: babyData, timeoutSeconds: 45)

            // Process each token as it arrives
            for try await token in stream {
                // Update the message content on the main thread
                DispatchQueue.main.async {
                    streamingMessage.content += token
                    streamingMessage.updatedAt = Date()

                    // Save the updated content periodically
                    if streamingMessage.content.count % 20 == 0 {
                        try? self.modelContext?.save()
                    }
                }
            }

            // Complete the streaming message
            DispatchQueue.main.async {
                streamingMessage.messageState = .complete
                streamingMessage.tokenCount = streamingMessage.content.count / 4 // Rough estimate
                self.isStreaming = false
                try? self.modelContext?.save() // Save the updated message

                // Generate new suggested questions after receiving a response
                self.generateSuggestedQuestions()
            }

        } catch {
            // Handle errors
            DispatchQueue.main.async {
                self.isStreaming = false
                self.errorMessage = "Error: \(error.localizedDescription)"
                print("LLM Streaming Error: \(error)")

                // Update the message to show the error
                streamingMessage.content += "\n\n[Error: Unable to complete response]"
                streamingMessage.messageState = .complete
                try? self.modelContext?.save() // Save the updated message
            }
        }
    }

    func addMessage(_ message: ChatMessage) {
        guard let modelContext = modelContext else {
            errorMessage = "Model context not available"
            print("Error: Model context not available when adding message")
            return
        }

        // Insert the message into the model context
        modelContext.insert(message)

        do {
            // Save the model context
            try modelContext.save()

            // Update the messages array on the main thread
            DispatchQueue.main.async {
                // Check if the message is already in the array to avoid duplicates
                if !self.messages.contains(where: { $0.id == message.id }) {
                    self.messages.append(message)
                    print("Message added: \(message.content.prefix(50))\(message.content.count > 50 ? "..." : "")")
                }
            }
        } catch {
            errorMessage = "Failed to save message: \(error.localizedDescription)"
            print("Error saving message: \(error.localizedDescription)")
        }
    }

    // Function removed as we're using direct modelContext.save() calls

    func askSuggestedQuestion(_ question: String) {
        inputText = question
        sendMessage()
    }

    func deleteThread() {
        guard let thread = thread, let modelContext = modelContext else { return }

        modelContext.delete(thread)

        do {
            try modelContext.save()
            print("Thread deleted")
        } catch {
            errorMessage = "Failed to delete thread: \(error.localizedDescription)"
            print("Error deleting thread: \(error.localizedDescription)")
        }
    }

    private func generateSuggestedQuestions() {
        // Always start with default questions to ensure we have something to show
        self.suggestedQuestions = defaultSuggestedQuestions
        
        guard let baby = thread?.baby, let modelContext = modelContext else {
            // If no baby or model context, stick with default questions
            return
        }

        // First, try to generate questions based on conversation history if we have enough messages
        if messages.count >= 2 {
            generateSuggestedQuestionsFromConversation(for: baby)
            return
        }

        // If we don't have enough conversation history, try to use insights
        do {
            var insightDescriptor = FetchDescriptor<Insight>(
                sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
            )
            insightDescriptor.fetchLimit = 3

            let recentInsights = try modelContext.fetch(insightDescriptor)
                .filter { (insight: Insight) -> Bool in insight.baby?.id == baby.id }

            // If we have recent insights, generate questions based on them
            if !recentInsights.isEmpty {
                // Extract topics from recent insights
                let insightTopics = recentInsights.compactMap { $0.title }

                // Generate questions based on recent insights
                var questions: [String] = []

                for topic in insightTopics {
                    if topic.lowercased().contains("feeding") || topic.lowercased().contains("eat") {
                        questions.append("Tell me more about \(baby.name)'s feeding patterns")
                        questions.append("How much should \(baby.name) be eating at this age?")
                    }

                    if topic.lowercased().contains("sleep") {
                        questions.append("How can I improve \(baby.name)'s sleep routine?")
                        questions.append("Is \(baby.name) getting enough sleep?")
                    }

                    if topic.lowercased().contains("diaper") || topic.lowercased().contains("poop") {
                        questions.append("Is \(baby.name)'s diaper pattern normal?")
                        questions.append("What should healthy baby poop look like?")
                    }

                    if topic.lowercased().contains("development") || topic.lowercased().contains("milestone") {
                        questions.append("What milestones should \(baby.name) reach next?")
                        questions.append("How can I support \(baby.name)'s development?")
                    }
                }

                // Add some general questions
                questions.append("What activities are good for \(baby.name) right now?")

                // Take up to 5 unique questions
                let uniqueQuestions = Array(Set(questions))
                if uniqueQuestions.count >= 5 {
                    suggestedQuestions = Array(uniqueQuestions.prefix(5))
                    return
                } else if !uniqueQuestions.isEmpty {
                    // If we have some but not enough questions, use them and fill in with age-based questions
                    suggestedQuestions = uniqueQuestions
                    return
                }
            }
        } catch {
            print("Error fetching insights for suggested questions: \(error)")
        }

        // Fall back to age-based questions if we couldn't generate insight-based questions
        generateAgeBasedSuggestedQuestions(for: baby)
    }

    /// Generate suggested questions based on conversation history
    private func generateSuggestedQuestionsFromConversation(for baby: Baby) {
        // Only proceed if we have messages
        guard !messages.isEmpty, let lastMessage = messages.last, lastMessage.chatRole == .assistant else {
            generateAgeBasedSuggestedQuestions(for: baby)
            return
        }

        // Get the conversation history
        let conversationHistory = messages.suffix(6) // Use last 6 messages for context

        // Format the conversation for the LLM
        var conversationText = "Conversation history:\n"
        for message in conversationHistory {
            let role = message.chatRole == .user ? "User" : "Assistant"
            conversationText += "\(role): \(message.content)\n"
        }

        // Create a prompt for generating follow-up questions
        let prompt = """
        Based on the conversation history below, generate 5 relevant follow-up questions that the user might want to ask about their baby \(baby.name).

        \(conversationText)

        Generate 5 short, specific questions that would be natural follow-ups to this conversation. Each question should be on a new line and start with a number followed by a period (e.g., "1. Question here").
        Focus on questions related to baby care, development, health, or parenting advice that would be helpful for the parent.

        Format your response as a simple numbered list with no additional text or explanations:
        1. First question here?
        2. Second question here?
        3. Third question here?
        4. Fourth question here?
        5. Fifth question here?
        """

        // Get baby data for context
        let babyData = getBabyDataContext(for: baby)

        // Use the LLM to generate questions
        llmService.generateResponse(userMessage: prompt, babyData: babyData) { [weak self] result in
            guard let self = self else { return }

            switch result {
            case .success(let response):
                // Parse the response to extract questions
                let questions = self.parseQuestionsFromResponse(response)

                if !questions.isEmpty {
                    DispatchQueue.main.async {
                        self.suggestedQuestions = questions
                    }
                }
            case .failure(let error):
                print("Error generating suggested questions: \(error)")
                // Fall back to age-based questions
                DispatchQueue.main.async {
                    self.generateAgeBasedSuggestedQuestions(for: baby)
                }
            }
        }
    }

    /// Parse questions from LLM response
    private func parseQuestionsFromResponse(_ response: String) -> [String] {
        // Split the response by newlines
        let lines = response.split(separator: "\n")

        // Extract lines that look like questions (start with a number or have a question mark)
        var questions: [String] = []

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)

            // Skip empty lines
            if trimmedLine.isEmpty {
                continue
            }

            // Check if the line starts with a number followed by a period or has a question mark
            if trimmedLine.range(of: #"^\d+\.\s+"#, options: .regularExpression) != nil || trimmedLine.contains("?") {
                // Remove the number prefix if present
                let cleanedQuestion = trimmedLine.replacingOccurrences(of: #"^\d+\.\s+"#, with: "", options: .regularExpression)

                // Add the question if it's not empty
                if !cleanedQuestion.isEmpty {
                    questions.append(cleanedQuestion)
                }
            }
        }

        // If we couldn't extract any questions, try to use the whole response
        if questions.isEmpty && !response.isEmpty {
            // Split by periods and take sentences that end with a question mark
            let sentences = response.split(separator: ".")
            for sentence in sentences {
                let trimmed = sentence.trimmingCharacters(in: .whitespacesAndNewlines)
                if trimmed.contains("?") {
                    questions.append(trimmed)
                }
            }
        }

        // Take up to 5 questions
        return Array(questions.prefix(5))
    }

    private func generateAgeBasedSuggestedQuestions(for baby: Baby) {
        // Generate questions based on baby's age
        let ageInDays = baby.ageInDays

        if ageInDays < 30 { // Newborn
            suggestedQuestions = [
                "How often should \(baby.name) be feeding?",
                "Is \(baby.name) sleeping enough?",
                "What's normal for newborn diaper changes?",
                "When should we have our next checkup?",
                "How to soothe \(baby.name) when crying?"
            ]
        } else if ageInDays < 90 { // 1-3 months
            suggestedQuestions = [
                "When will \(baby.name) start sleeping longer?",
                "How much should \(baby.name) be eating now?",
                "What milestones should I expect soon?",
                "Is \(baby.name)'s weight gain normal?",
                "How to establish a sleep routine?"
            ]
        } else if ageInDays < 180 { // 3-6 months
            suggestedQuestions = [
                "When should we start solid foods?",
                "How to prepare for teething?",
                "What toys are best for \(baby.name) now?",
                "How to encourage tummy time?",
                "Is \(baby.name)'s development on track?"
            ]
        } else { // 6+ months
            suggestedQuestions = [
                "What solid foods should I introduce next?",
                "How to handle separation anxiety?",
                "When should \(baby.name) start crawling?",
                "How to baby-proof our home?",
                "What's a good feeding schedule now?"
            ]
        }
    }

    private var defaultSuggestedQuestions: [String] {
        return [
            "Is my baby sleeping enough?",
            "How much should my baby be eating?",
            "When should I be concerned about diaper changes?",
            "What are normal growth patterns?",
            "How can I establish a better sleep routine?"
        ]
    }

    private func getBabyDataContext(for baby: Baby) -> String {
        // Get recent feeding, sleep, diaper entries
        guard let modelContext = modelContext else {
            return "No data available"
        }

        // Use the BabyContextSummariser to get a condensed summary
        let summariser = BabyContextSummariser(modelContext: modelContext)
        return summariser.generateSummary(for: baby.id)
    }

    private func formatTimeAgo(_ date: Date) -> String {
        let now = Date()
        let components = Calendar.current.dateComponents([.minute, .hour, .day], from: date, to: now)

        if let day = components.day, day > 0 {
            return "\(day) day\(day == 1 ? "" : "s") ago"
        } else if let hour = components.hour, hour > 0 {
            return "\(hour) hour\(hour == 1 ? "" : "s") ago"
        } else if let minute = components.minute, minute > 0 {
            return "\(minute) minute\(minute == 1 ? "" : "s") ago"
        } else {
            return "Just now"
        }
    }
}

