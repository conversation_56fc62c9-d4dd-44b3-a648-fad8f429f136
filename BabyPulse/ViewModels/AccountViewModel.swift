import Foundation
import SwiftData
import Combine

class AccountViewModel: ObservableObject {
    // MARK: - Properties

    // Services
    private let supabaseService = SupabaseService.shared
    private let revenueCatService = RevenueCatService.shared

    // Model context
    var modelContext: ModelContext?

    // User account
    @Published var userAccount: UserAccount?

    // Authentication state
    @Published var isAuthenticated = false
    @Published var isLoading = false
    @Published var errorMessage: String?

    // Form fields
    @Published var email = ""
    @Published var password = ""
    @Published var confirmPassword = ""

    // Validation
    @Published var emailError: String?
    @Published var passwordError: String?
    @Published var confirmPasswordError: String?

    // Cancellables
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization

    init() {
        // Subscribe to authentication state changes
        supabaseService.$isAuthenticated
            .sink { [weak self] isAuthenticated in
                self?.isAuthenticated = isAuthenticated
                if isAuthenticated {
                    Task {
                        await self?.loadUserAccount()
                    }
                }
            }
            .store(in: &cancellables)

        // Subscribe to subscription status changes
        revenueCatService.$subscriptionStatus
            .sink { [weak self] status in
                Task {
                    await self?.updateSubscriptionStatus(status)
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - Methods

    /// Load user account from SwiftData
    @MainActor
    func loadUserAccount() async {
        guard let modelContext = modelContext else { return }

        isLoading = true
        errorMessage = nil

        do {
            // Check if we have a user account for the current Supabase user
            if let supabaseUserId = supabaseService.currentUser?.id {
                // Convert UUID to String for comparison
                let supabaseUserIdString = supabaseUserId.uuidString

                let descriptor = FetchDescriptor<UserAccount>(
                    predicate: #Predicate<UserAccount> { account in
                        account.supabaseUserId == supabaseUserIdString
                    }
                )

                let accounts = try modelContext.fetch(descriptor)

                if let account = accounts.first {
                    // Update existing account
                    userAccount = account
                } else {
                    // Create new account
                    let email = supabaseService.currentUser?.email ?? ""
                    let newAccount = UserAccount(
                        email: email,
                        isEmailVerified: false,
                        supabaseUserId: supabaseUserIdString,
                        subscriptionStatus: .free
                    )

                    modelContext.insert(newAccount)
                    try modelContext.save()

                    userAccount = newAccount
                }
            }

            isLoading = false
        } catch {
            errorMessage = "Failed to load user account: \(error.localizedDescription)"
            isLoading = false
            print("Error loading user account: \(error)")
        }
    }

    /// Update subscription status in SwiftData
    @MainActor
    func updateSubscriptionStatus(_ status: RevenueCatService.SubscriptionStatus) async {
        guard let modelContext = modelContext, let account = userAccount else { return }

        // Map RevenueCatService.SubscriptionStatus to UserAccount.SubscriptionStatus
        let mappedStatus: UserAccount.SubscriptionStatus
        switch status {
        case .free:
            mappedStatus = .free
        case .trial:
            mappedStatus = .trial
        case .premium:
            mappedStatus = .premium
        case .expired:
            mappedStatus = .expired
        }

        // Update account
        account.subscriptionStatus = mappedStatus
        account.updatedAt = Date()

        // Save changes
        do {
            try modelContext.save()
        } catch {
            print("Error updating subscription status: \(error)")
        }
    }

    /// Sign up a new user
    @MainActor
    func signUp() async -> Bool {
        // Validate form
        guard validateForm() else { return false }

        isLoading = true
        errorMessage = nil

        // Sign up with Supabase
        let success = await supabaseService.signUp(email: email, password: password)

        if success {
            // Load user account
            await loadUserAccount()
        }

        isLoading = false
        return success
    }

    /// Sign in an existing user
    @MainActor
    func signIn() async -> Bool {
        // Validate email and password
        guard validateEmailAndPassword() else { return false }

        isLoading = true
        errorMessage = nil

        // Sign in with Supabase
        let success = await supabaseService.signIn(email: email, password: password)

        if success {
            // Load user account
            await loadUserAccount()
        }

        isLoading = false
        return success
    }

    /// Sign out the current user
    @MainActor
    func signOut() async -> Bool {
        isLoading = true
        errorMessage = nil

        // Sign out with Supabase
        let success = await supabaseService.signOut()

        if success {
            // Clear user account
            userAccount = nil
        }

        isLoading = false
        return success
    }

    /// Validate form
    private func validateForm() -> Bool {
        // Reset errors
        emailError = nil
        passwordError = nil
        confirmPasswordError = nil

        // Validate email
        if email.isEmpty {
            emailError = "Email is required"
            return false
        }

        if !isValidEmail(email) {
            emailError = "Invalid email format"
            return false
        }

        // Validate password
        if password.isEmpty {
            passwordError = "Password is required"
            return false
        }

        if password.count < 8 {
            passwordError = "Password must be at least 8 characters"
            return false
        }

        // Validate confirm password
        if confirmPassword.isEmpty {
            confirmPasswordError = "Please confirm your password"
            return false
        }

        if password != confirmPassword {
            confirmPasswordError = "Passwords do not match"
            return false
        }

        return true
    }

    /// Validate email and password
    private func validateEmailAndPassword() -> Bool {
        // Reset errors
        emailError = nil
        passwordError = nil

        // Validate email
        if email.isEmpty {
            emailError = "Email is required"
            return false
        }

        if !isValidEmail(email) {
            emailError = "Invalid email format"
            return false
        }

        // Validate password
        if password.isEmpty {
            passwordError = "Password is required"
            return false
        }

        return true
    }

    /// Check if email is valid
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegEx = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPred = NSPredicate(format:"SELF MATCHES %@", emailRegEx)
        return emailPred.evaluate(with: email)
    }

    /// Check authentication status
    @MainActor
    func checkAuthentication(using service: SupabaseService) async {
        isLoading = true
        errorMessage = nil

        // Check if user is authenticated with Supabase
        let isAuth = await service.checkSession()

        if isAuth {
            // Load user account
            await loadUserAccount()
        }

        isLoading = false
    }

    /// Send email verification
    @MainActor
    func sendEmailVerification() async -> Bool {
        isLoading = true
        errorMessage = nil

        // Send verification email with Supabase
        let success = await supabaseService.sendEmailVerification()

        isLoading = false
        return success
    }

    /// Reset password
    @MainActor
    func resetPassword(email: String) async -> Bool {
        isLoading = true
        errorMessage = nil

        // Send password reset email with Supabase
        let success = await supabaseService.resetPassword(email: email)

        isLoading = false
        return success
    }

    /// Refresh session
    @MainActor
    func refreshSession() async -> Bool {
        isLoading = true
        errorMessage = nil

        // Refresh session with Supabase
        let success = await supabaseService.refreshSession()

        isLoading = false
        return success
    }

    // Password reset email
    @Published var passwordResetEmail = ""
    @Published var showPasswordResetAlert = false
}
