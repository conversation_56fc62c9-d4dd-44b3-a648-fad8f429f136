//
//  LogEntryViewModel.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData

class LogEntryViewModel: ObservableObject {
    // Sync manager for Supabase sync
    private let syncManager = SyncManager.shared
    @Published var selectedCategory: LogCategory?
    @Published var timestamp: Date = Date()
    @Published var notes: String = ""

    // Edit mode properties
    @Published var isEditMode: Bool = false
    var entryToEdit: Any?

    // Feeding-specific properties
    @Published var feedingType: FeedingEntry.FeedingType = .breastfeeding
    @Published var feedingDuration: Int = 15
    @Published var feedingVolume: String = ""
    @Published var bottleContent: FeedingEntry.BottleContent = .formula
    @Published var leftBreast: Bool = false
    @Published var rightBreast: Bool = false
    @Published var foodItem: String = ""
    @Published var foodAmount: String = ""
    @Published var reaction: String = ""

    // Diaper-specific properties
    @Published var diaperType: DiaperEntry.DiaperType = .wet
    @Published var poopColor: DiaperEntry.PoopColor = .yellow
    @Published var poopConsistency: DiaperEntry.PoopConsistency = .seedy

    // Sleep-specific properties
    @Published var sleepStartTime: Date = Date()
    @Published var sleepEndTime: Date = Date().addingTimeInterval(3600) // Default to 1 hour
    @Published var sleepOngoing: Bool = false
    @Published var sleepLocation: SleepEntry.SleepLocation = .crib

    // Growth-specific properties
    @Published var weight: String = ""
    @Published var height: String = ""
    @Published var headCircumference: String = ""

    // Health-specific properties
    @Published var healthEntryType: HealthEntry.HealthEntryType = .temperature
    @Published var temperature: String = ""
    @Published var temperatureUnit: HealthEntry.TemperatureUnit = UnitConverter.temperatureUnitFor(unitSystem: UserPreferencesManager.shared.unitSystem)
    @Published var medicationName: String = ""
    @Published var medicationDosage: String = ""
    @Published var selectedSymptoms: [HealthEntry.Symptom] = []
    @Published var vaccineName: String = ""
    @Published var appointmentReason: String = ""
    @Published var appointmentProvider: String = ""

    // General Activity-specific properties
    @Published var generalActivityType: GeneralActivityType = .bath // Default to a common one
    @Published var customGeneralActivityName: String = ""
    @Published var generalActivityDurationMinutes: Int = 0 // 0 means not set or no duration
    @Published var generalActivityCustomNameError: String? // For custom activity name validation

    // Validation
    @Published var feedingVolumeError: String?
    @Published var foodItemError: String?
    @Published var weightError: String?
    @Published var heightError: String?
    @Published var headCircumferenceError: String?
    @Published var temperatureError: String?
    @Published var medicationNameError: String?
    @Published var vaccineNameError: String?
    @Published var appointmentReasonError: String?

    // Current baby
    var currentBaby: Baby?

    // MARK: - Initializers

    init(category: LogCategory? = nil) {
        self.selectedCategory = category
        self.isEditMode = false
    }

    // Initializer for editing a feeding entry
    init(feedingEntry: FeedingEntry) {
        self.selectedCategory = .feeding
        self.isEditMode = true
        self.entryToEdit = feedingEntry
        self.timestamp = feedingEntry.timestamp
        self.notes = feedingEntry.notes ?? ""
        self.currentBaby = feedingEntry.baby

        // Set feeding-specific properties
        self.feedingType = feedingEntry.type

        switch feedingEntry.type {
        case .breastfeeding:
            self.feedingDuration = feedingEntry.duration ?? 15
            self.leftBreast = feedingEntry.leftBreast ?? false
            self.rightBreast = feedingEntry.rightBreast ?? false

        case .bottleFeeding:
            if let volume = feedingEntry.volume {
                self.feedingVolume = String(format: "%.0f", volume)
            }
            self.bottleContent = feedingEntry.content ?? .formula

        case .solidFood:
            self.foodItem = feedingEntry.foodItem ?? ""
            self.foodAmount = feedingEntry.foodAmount ?? ""
            self.reaction = feedingEntry.reaction ?? ""
        }
    }

    // Initializer for editing a diaper entry
    init(diaperEntry: DiaperEntry) {
        self.selectedCategory = .diaper
        self.isEditMode = true
        self.entryToEdit = diaperEntry
        self.timestamp = diaperEntry.timestamp
        self.notes = diaperEntry.notes ?? ""
        self.currentBaby = diaperEntry.baby

        // Set diaper-specific properties
        self.diaperType = diaperEntry.type

        if diaperEntry.type == .dirty || diaperEntry.type == .mixed {
            self.poopColor = diaperEntry.poopColor ?? .yellow
            self.poopConsistency = diaperEntry.poopConsistency ?? .seedy
        }
    }

    // Initializer for editing a sleep entry
    init(sleepEntry: SleepEntry) {
        self.selectedCategory = .sleep
        self.isEditMode = true
        self.entryToEdit = sleepEntry
        self.timestamp = sleepEntry.timestamp
        self.notes = sleepEntry.notes ?? ""
        self.currentBaby = sleepEntry.baby

        // Set sleep-specific properties
        self.sleepStartTime = sleepEntry.timestamp
        self.sleepOngoing = sleepEntry.endTime == nil
        if let endTime = sleepEntry.endTime {
            self.sleepEndTime = endTime
        }
        self.sleepLocation = sleepEntry.location ?? .crib
    }

    // Initializer for editing a growth entry
    init(growthEntry: GrowthEntry) {
        self.selectedCategory = .growth
        self.isEditMode = true
        self.entryToEdit = growthEntry
        self.timestamp = growthEntry.timestamp
        self.notes = growthEntry.notes ?? ""
        self.currentBaby = growthEntry.baby

        // Set growth-specific properties
        let unitSystem = UserPreferencesManager.shared.unitSystem

        if let weight = growthEntry.weight {
            let displayWeight = unitSystem == .metric ? weight : UnitConverter.kgToPounds(weight)
            self.weight = String(format: "%.1f", displayWeight)
        }

        if let height = growthEntry.height {
            let displayHeight = unitSystem == .metric ? height : UnitConverter.cmToInches(height)
            self.height = String(format: "%.1f", displayHeight)
        }

        if let headCircumference = growthEntry.headCircumference {
            let displayHC = unitSystem == .metric ? headCircumference : UnitConverter.cmToInches(headCircumference)
            self.headCircumference = String(format: "%.1f", displayHC)
        }
    }

    // Initializer for editing a health entry
    init(healthEntry: HealthEntry) {
        self.selectedCategory = .health
        self.isEditMode = true
        self.entryToEdit = healthEntry
        self.timestamp = healthEntry.timestamp
        self.notes = healthEntry.notes ?? ""
        self.currentBaby = healthEntry.baby

        // Set health-specific properties
        self.healthEntryType = healthEntry.type

        switch healthEntry.type {
        case .temperature:
            if let temperature = healthEntry.temperature {
                self.temperature = String(format: "%.1f", temperature)
            }
            self.temperatureUnit = healthEntry.temperatureUnit ?? .celsius

        case .medication:
            self.medicationName = healthEntry.medicationName ?? ""
            self.medicationDosage = healthEntry.medicationDosage ?? ""

        case .symptom:
            self.selectedSymptoms = healthEntry.getSymptoms()

        case .vaccination:
            self.vaccineName = healthEntry.vaccineName ?? ""

        case .appointment:
            self.appointmentReason = healthEntry.appointmentReason ?? ""
            self.appointmentProvider = healthEntry.appointmentProvider ?? ""
        }
    }

    // Initializer for editing a general activity entry
    init(generalActivityEntry: GeneralActivityEntry) {
        self.selectedCategory = .general
        self.isEditMode = true
        self.entryToEdit = generalActivityEntry
        self.timestamp = generalActivityEntry.timestamp
        self.notes = generalActivityEntry.notes ?? ""
        self.currentBaby = generalActivityEntry.baby

        // Set general activity-specific properties
        self.generalActivityType = generalActivityEntry.activityType
        if generalActivityEntry.activityType == .other {
            self.customGeneralActivityName = generalActivityEntry.customActivityName ?? ""
        }
        self.generalActivityDurationMinutes = generalActivityEntry.durationMinutes ?? 0
    }

    func setCurrentBaby(_ baby: Baby) {
        self.currentBaby = baby
    }

    func validateFeedingEntry() -> Bool {
        // Reset errors
        feedingVolumeError = nil
        foodItemError = nil

        switch feedingType {
        case .bottleFeeding:
            if feedingVolume.isEmpty {
                feedingVolumeError = "Please enter the volume"
                return false
            }

            if let volume = Double(feedingVolume), volume <= 0 {
                feedingVolumeError = "Volume must be greater than 0"
                return false
            }

        case .solidFood:
            if foodItem.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                foodItemError = "Please enter the food item"
                return false
            }

        case .breastfeeding:
            if !leftBreast && !rightBreast {
                return false
            }
        }

        return true
    }

    func validateDiaperEntry() -> Bool {
        // For diaper entries, all types are valid without additional validation
        return true
    }

    func validateSleepEntry() -> Bool {
        // For sleep entries, validate that end time is after start time if not ongoing
        if !sleepOngoing && sleepEndTime <= sleepStartTime {
            return false
        }

        return true
    }

    func validateGrowthEntry() -> Bool {
        // Reset errors
        weightError = nil
        heightError = nil
        headCircumferenceError = nil

        // Get the current unit system
        let unitSystem = UserPreferencesManager.shared.unitSystem

        // At least one measurement must be provided
        if weight.isEmpty && height.isEmpty && headCircumference.isEmpty {
            weightError = "At least one measurement is required"
            return false
        }

        // Validate weight if provided
        if !weight.isEmpty {
            if let weightValue = Double(weight) {
                if unitSystem == .metric {
                    // Metric validation (kg)
                    if weightValue <= 0 || weightValue > 50 { // Reasonable range for baby weight in kg
                        weightError = "Weight must be between 0 and 50 kg"
                        return false
                    }
                } else {
                    // Imperial validation (lb)
                    if weightValue <= 0 || weightValue > 110 { // Reasonable range for baby weight in lb
                        weightError = "Weight must be between 0 and 110 lb"
                        return false
                    }
                }
            } else {
                weightError = "Please enter a valid number"
                return false
            }
        }

        // Validate height if provided
        if !height.isEmpty {
            if let heightValue = Double(height) {
                if unitSystem == .metric {
                    // Metric validation (cm)
                    if heightValue <= 0 || heightValue > 150 { // Reasonable range for baby height in cm
                        heightError = "Height must be between 0 and 150 cm"
                        return false
                    }
                } else {
                    // Imperial validation (in)
                    if heightValue <= 0 || heightValue > 60 { // Reasonable range for baby height in inches
                        heightError = "Height must be between 0 and 60 in"
                        return false
                    }
                }
            } else {
                heightError = "Please enter a valid number"
                return false
            }
        }

        // Validate head circumference if provided
        if !headCircumference.isEmpty {
            if let headCircumferenceValue = Double(headCircumference) {
                if unitSystem == .metric {
                    // Metric validation (cm)
                    if headCircumferenceValue <= 0 || headCircumferenceValue > 100 { // Reasonable range for head circumference in cm
                        headCircumferenceError = "Head circumference must be between 0 and 100 cm"
                        return false
                    }
                } else {
                    // Imperial validation (in)
                    if headCircumferenceValue <= 0 || headCircumferenceValue > 40 { // Reasonable range for head circumference in inches
                        headCircumferenceError = "Head circumference must be between 0 and 40 in"
                        return false
                    }
                }
            } else {
                headCircumferenceError = "Please enter a valid number"
                return false
            }
        }

        return true
    }

    func validateHealthEntry() -> Bool {
        // Reset errors
        temperatureError = nil
        medicationNameError = nil
        vaccineNameError = nil
        appointmentReasonError = nil

        switch healthEntryType {
        case .temperature:
            if temperature.isEmpty {
                temperatureError = "Please enter a temperature"
                return false
            }

            if let temperatureValue = Double(temperature) {
                if temperatureUnit == .celsius {
                    if temperatureValue < 35 || temperatureValue > 42 { // Reasonable range for body temperature in Celsius
                        temperatureError = "Temperature must be between 35°C and 42°C"
                        return false
                    }
                } else { // Fahrenheit
                    if temperatureValue < 95 || temperatureValue > 108 { // Reasonable range for body temperature in Fahrenheit
                        temperatureError = "Temperature must be between 95°F and 108°F"
                        return false
                    }
                }
            } else {
                temperatureError = "Please enter a valid number"
                return false
            }

        case .medication:
            if medicationName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                medicationNameError = "Please enter the medication name"
                return false
            }

        case .symptom:
            if selectedSymptoms.isEmpty {
                return false
            }

        case .vaccination:
            if vaccineName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                vaccineNameError = "Please enter the vaccine name"
                return false
            }

        case .appointment:
            if appointmentReason.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                appointmentReasonError = "Please enter the reason for the appointment"
                return false
            }
        }

        return true
    }

    func validateGeneralActivityEntry() -> Bool {
        if generalActivityType == .other && customGeneralActivityName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            generalActivityCustomNameError = "Please enter a name for the custom activity."
            return false
        }
        generalActivityCustomNameError = nil
        return true
    }

    func resetFormProperties() {
        timestamp = Date()
        notes = ""
        isEditMode = false
        entryToEdit = nil

        // Reset feeding
        feedingType = .breastfeeding
        feedingDuration = 15
        feedingVolume = ""
        bottleContent = .formula
        leftBreast = false
        rightBreast = false
        foodItem = ""
        foodAmount = ""
        reaction = ""

        // Reset diaper
        diaperType = .wet
        poopColor = .yellow
        poopConsistency = .seedy

        // Reset sleep
        sleepStartTime = Date()
        sleepEndTime = Date().addingTimeInterval(3600)
        sleepOngoing = false
        sleepLocation = .crib

        // Reset growth
        weight = ""
        height = ""
        headCircumference = ""

        // Reset health
        healthEntryType = .temperature
        temperature = ""
        temperatureUnit = UnitConverter.temperatureUnitFor(unitSystem: UserPreferencesManager.shared.unitSystem)
        medicationName = ""
        medicationDosage = ""
        selectedSymptoms = []
        vaccineName = ""
        appointmentReason = ""
        appointmentProvider = ""

        // Reset General Activity properties
        generalActivityType = .bath // Default
        customGeneralActivityName = ""
        generalActivityDurationMinutes = 0

        // Reset errors
        feedingVolumeError = nil
        foodItemError = nil
        weightError = nil
        heightError = nil
        headCircumferenceError = nil
        temperatureError = nil
        medicationNameError = nil
        vaccineNameError = nil
        appointmentReasonError = nil
        generalActivityCustomNameError = nil
    }

    func saveFeedingEntry(modelContext: ModelContext) -> Bool {
        guard validateFeedingEntry(), let baby = currentBaby else { return false }

        let ozToMlConversionFactor = 29.5735
        var volumeInMl: Double?

        if let enteredVolume = Double(feedingVolume) {
            if UserPreferencesManager.shared.unitSystem == .imperial {
                volumeInMl = enteredVolume * ozToMlConversionFactor
            } else {
                volumeInMl = enteredVolume
            }
        } else if !feedingVolume.isEmpty { // If not empty but not a valid double
            return false // Or handle error appropriately
        }
        // If feedingVolume is empty, volumeInMl will remain nil, which is acceptable if volume is optional.
        // However, validateFeedingEntry() should catch empty required volume.

        // Check if we're in edit mode
        if isEditMode, let existingEntry = entryToEdit as? FeedingEntry {
            // Update existing entry
            existingEntry.timestamp = timestamp
            existingEntry.notes = notes.isEmpty ? nil : notes
            existingEntry.updatedAt = Date()
            existingEntry.type = feedingType

            // Update type-specific properties
            switch feedingType {
            case .breastfeeding:
                existingEntry.duration = feedingDuration
                existingEntry.leftBreast = leftBreast
                existingEntry.rightBreast = rightBreast
                // Clear properties from other types
                existingEntry.volume = nil
                existingEntry.content = nil
                existingEntry.foodItem = nil
                existingEntry.foodAmount = nil
                existingEntry.reaction = nil

            case .bottleFeeding:
                if let finalVolume = volumeInMl {
                    existingEntry.volume = finalVolume
                    existingEntry.content = bottleContent
                } else {
                    // This case should ideally be caught by validateFeedingEntry if volume is mandatory
                    // For bottle feeding, volume is typically required.
                    // If feedingVolume was empty, validateFeedingEntry should have returned false.
                    // If it was non-empty but invalid, volumeInMl would be nil (after failing Double conversion).
                    return false // Or specific error handling
                }
                // Clear properties from other types
                existingEntry.duration = nil
                existingEntry.leftBreast = nil
                existingEntry.rightBreast = nil
                existingEntry.foodItem = nil
                existingEntry.foodAmount = nil
                existingEntry.reaction = nil

            case .solidFood:
                existingEntry.foodItem = foodItem
                existingEntry.foodAmount = foodAmount.isEmpty ? nil : foodAmount
                existingEntry.reaction = reaction.isEmpty ? nil : reaction
                // Clear properties from other types
                existingEntry.duration = nil
                existingEntry.leftBreast = nil
                existingEntry.rightBreast = nil
                existingEntry.volume = nil
                existingEntry.content = nil
            }

            do {
                try modelContext.save()

                // Post notification for insight generation
                NotificationCenter.default.post(name: .feedingEntryUpdated, object: existingEntry)

                // Sync with Supabase - wrapped in a Task to avoid blocking the UI
                Task {
                    do {
                        try await syncManager.syncFeedingEntry(entry: existingEntry)
                    } catch {
                        print("Error syncing updated feeding entry: \(error)")
                    }
                }

                return true
            } catch {
                print("Error updating feeding entry: \(error)")
                return false
            }
        } else {
            // Create new entry
            let entry: FeedingEntry

            switch feedingType {
            case .breastfeeding:
                entry = FeedingEntry.createBreastfeeding(
                    timestamp: timestamp,
                    duration: feedingDuration,
                    leftBreast: leftBreast,
                    rightBreast: rightBreast,
                    notes: notes.isEmpty ? nil : notes,
                    baby: baby
                )

            case .bottleFeeding:
                if let finalVolume = volumeInMl {
                    entry = FeedingEntry.createBottleFeeding(
                        timestamp: timestamp,
                        volume: finalVolume,
                        content: bottleContent,
                        notes: notes.isEmpty ? nil : notes,
                        baby: baby
                    )
                } else {
                    // This case should ideally be caught by validateFeedingEntry if volume is mandatory
                    return false // Or specific error handling
                }

            case .solidFood:
                entry = FeedingEntry.createSolidFood(
                    timestamp: timestamp,
                    foodItem: foodItem,
                    foodAmount: foodAmount.isEmpty ? nil : foodAmount,
                    reaction: reaction.isEmpty ? nil : reaction,
                    notes: notes.isEmpty ? nil : notes,
                    baby: baby
                )
            }

            modelContext.insert(entry)

            do {
                try modelContext.save()

                // Post notification for insight generation
                NotificationCenter.default.post(name: .feedingEntryAdded, object: entry)

                // Sync with Supabase - wrapped in a Task to avoid blocking the UI
                Task {
                    do {
                        try await syncManager.syncFeedingEntry(entry: entry)
                    } catch {
                        print("Error syncing feeding entry: \(error)")
                        // This is non-blocking, so we still return success for the local save
                    }
                }

                return true
            } catch {
                print("Error saving feeding entry: \(error)")
                return false
            }
        }
    }

    func saveDiaperEntry(modelContext: ModelContext) -> Bool {
        guard validateDiaperEntry(), let baby = currentBaby else { return false }

        // Check if we're in edit mode
        if isEditMode, let existingEntry = entryToEdit as? DiaperEntry {
            // Update existing entry
            existingEntry.timestamp = timestamp
            existingEntry.notes = notes.isEmpty ? nil : notes
            existingEntry.updatedAt = Date()
            existingEntry.type = diaperType

            // Update type-specific properties
            switch diaperType {
            case .wet:
                // Clear properties from other types
                existingEntry.poopColor = nil
                existingEntry.poopConsistency = nil

            case .dirty, .mixed:
                existingEntry.poopColor = poopColor
                existingEntry.poopConsistency = poopConsistency
            }

            do {
                try modelContext.save()

                // Post notification for insight generation
                NotificationCenter.default.post(name: .diaperEntryUpdated, object: existingEntry)

                // Sync with Supabase - wrapped in a Task to avoid blocking the UI
                Task {
                    do {
                        try await syncManager.syncDiaperEntry(entry: existingEntry)
                    } catch {
                        print("Error syncing updated diaper entry: \(error)")
                    }
                }

                return true
            } catch {
                print("Error updating diaper entry: \(error)")
                return false
            }
        } else {
            // Create new entry
            let entry: DiaperEntry

            switch diaperType {
            case .wet:
                entry = DiaperEntry.createWet(
                    timestamp: timestamp,
                    notes: notes.isEmpty ? nil : notes,
                    baby: baby
                )

            case .dirty:
                entry = DiaperEntry.createDirty(
                    timestamp: timestamp,
                    poopColor: poopColor,
                    poopConsistency: poopConsistency,
                    notes: notes.isEmpty ? nil : notes,
                    baby: baby
                )

            case .mixed:
                entry = DiaperEntry.createMixed(
                    timestamp: timestamp,
                    poopColor: poopColor,
                    poopConsistency: poopConsistency,
                    notes: notes.isEmpty ? nil : notes,
                    baby: baby
                )
            }

            modelContext.insert(entry)

            do {
                try modelContext.save()

                // Post notification for insight generation
                NotificationCenter.default.post(name: .diaperEntryAdded, object: entry)

                // Sync with Supabase - wrapped in a Task to avoid blocking the UI
                Task {
                    do {
                        try await syncManager.syncDiaperEntry(entry: entry)
                    } catch {
                        print("Error syncing diaper entry: \(error)")
                        // This is non-blocking, so we still return success for the local save
                    }
                }

                return true
            } catch {
                print("Error saving diaper entry: \(error)")
                return false
            }
        }
    }

    func saveSleepEntry(modelContext: ModelContext) -> Bool {
        guard validateSleepEntry(), let baby = currentBaby else { return false }

        // Check if we're in edit mode
        if isEditMode, let existingEntry = entryToEdit as? SleepEntry {
            // Update existing entry
            existingEntry.timestamp = sleepStartTime
            existingEntry.notes = notes.isEmpty ? nil : notes
            existingEntry.updatedAt = Date()
            existingEntry.location = sleepLocation

            if sleepOngoing {
                existingEntry.endTime = nil
                existingEntry.duration = nil
            } else {
                existingEntry.endTime = sleepEndTime
                existingEntry.duration = Int(sleepEndTime.timeIntervalSince(sleepStartTime) / 60)
            }

            do {
                try modelContext.save()

                // Post notification for insight generation
                NotificationCenter.default.post(name: .sleepEntryUpdated, object: existingEntry)

                // Sync with Supabase - wrapped in a Task to avoid blocking the UI
                Task {
                    do {
                        try await syncManager.syncSleepEntry(entry: existingEntry)
                    } catch {
                        print("Error syncing updated sleep entry: \(error)")
                    }
                }

                return true
            } catch {
                print("Error updating sleep entry: \(error)")
                return false
            }
        } else {
            // Create new entry
            let entry: SleepEntry

            if sleepOngoing {
                entry = SleepEntry.createOngoingSleep(
                    startTime: sleepStartTime,
                    location: sleepLocation,
                    notes: notes.isEmpty ? nil : notes,
                    baby: baby
                )
            } else {
                entry = SleepEntry.createCompletedSleep(
                    startTime: sleepStartTime,
                    endTime: sleepEndTime,
                    location: sleepLocation,
                    notes: notes.isEmpty ? nil : notes,
                    baby: baby
                )
            }

            modelContext.insert(entry)

            do {
                try modelContext.save()

                // Post notification for insight generation
                NotificationCenter.default.post(name: .sleepEntryAdded, object: entry)

                // Sync with Supabase - wrapped in a Task to avoid blocking the UI
                Task {
                    do {
                        try await syncManager.syncSleepEntry(entry: entry)
                    } catch {
                        print("Error syncing sleep entry: \(error)")
                        // This is non-blocking, so we still return success for the local save
                    }
                }

                return true
            } catch {
                print("Error saving sleep entry: \(error)")
                return false
            }
        }
    }

    func saveGrowthEntry(modelContext: ModelContext) -> Bool {
        guard validateGrowthEntry(), let baby = currentBaby else { return false }

        // Get the current unit system
        let unitSystem = UserPreferencesManager.shared.unitSystem

        // Convert measurements to metric (canonical storage format) if needed
        var weightValue: Double? = nil
        var heightValue: Double? = nil
        var headCircumferenceValue: Double? = nil

        if !weight.isEmpty, let value = Double(weight) {
            // Convert from imperial to metric if needed
            weightValue = unitSystem == .metric ? value : UnitConverter.poundsToKg(value)
        }

        if !height.isEmpty, let value = Double(height) {
            // Convert from imperial to metric if needed
            heightValue = unitSystem == .metric ? value : UnitConverter.inchesToCm(value)
        }

        if !headCircumference.isEmpty, let value = Double(headCircumference) {
            // Convert from imperial to metric if needed
            headCircumferenceValue = unitSystem == .metric ? value : UnitConverter.inchesToCm(value)
        }

        // Check if we're in edit mode
        if isEditMode, let existingEntry = entryToEdit as? GrowthEntry {
            // Update existing entry
            existingEntry.timestamp = timestamp
            existingEntry.notes = notes.isEmpty ? nil : notes
            existingEntry.updatedAt = Date()
            existingEntry.weight = weightValue
            existingEntry.height = heightValue
            existingEntry.headCircumference = headCircumferenceValue

            do {
                try modelContext.save()

                // Post notification for insight generation
                NotificationCenter.default.post(name: .growthEntryUpdated, object: existingEntry)

                // Sync with Supabase - wrapped in a Task to avoid blocking the UI
                Task {
                    do {
                        try await syncManager.syncGrowthEntry(entry: existingEntry)
                    } catch {
                        print("Error syncing updated growth entry: \(error)")
                    }
                }

                return true
            } catch {
                print("Error updating growth entry: \(error)")
                return false
            }
        } else {
            // Create new entry
            let entry = GrowthEntry(
                timestamp: timestamp,
                weight: weightValue,
                height: heightValue,
                headCircumference: headCircumferenceValue,
                notes: notes.isEmpty ? nil : notes,
                baby: baby
            )

            modelContext.insert(entry)

            do {
                try modelContext.save()

                // Post notification for insight generation
                NotificationCenter.default.post(name: .growthEntryAdded, object: entry)

                // Sync with Supabase - wrapped in a Task to avoid blocking the UI
                Task {
                    do {
                        try await syncManager.syncGrowthEntry(entry: entry)
                    } catch {
                        print("Error syncing growth entry: \(error)")
                        // This is non-blocking, so we still return success for the local save
                    }
                }

                return true
            } catch {
                print("Error saving growth entry: \(error)")
                return false
            }
        }
    }

    func saveHealthEntry(modelContext: ModelContext) -> Bool {
        guard validateHealthEntry(), let baby = currentBaby else { return false }

        // Check if we're in edit mode
        if isEditMode, let existingEntry = entryToEdit as? HealthEntry {
            // Update existing entry
            existingEntry.timestamp = timestamp
            existingEntry.notes = notes.isEmpty ? nil : notes
            existingEntry.updatedAt = Date()
            existingEntry.type = healthEntryType

            // Reset all type-specific properties
            existingEntry.temperature = nil
            existingEntry.temperatureUnit = nil
            existingEntry.medicationName = nil
            existingEntry.medicationDosage = nil
            existingEntry.symptomsJSON = nil
            existingEntry.vaccineName = nil
            existingEntry.appointmentReason = nil
            existingEntry.appointmentProvider = nil

            // Set type-specific properties based on current type
            switch healthEntryType {
            case .temperature:
                if let temperatureValue = Double(temperature) {
                    existingEntry.temperature = temperatureValue
                    existingEntry.temperatureUnit = temperatureUnit
                }

            case .medication:
                existingEntry.medicationName = medicationName
                existingEntry.medicationDosage = medicationDosage.isEmpty ? nil : medicationDosage

            case .symptom:
                existingEntry.setSymptoms(selectedSymptoms)

            case .vaccination:
                existingEntry.vaccineName = vaccineName

            case .appointment:
                existingEntry.appointmentReason = appointmentReason
                existingEntry.appointmentProvider = appointmentProvider.isEmpty ? nil : appointmentProvider
            }

            do {
                try modelContext.save()

                // Post notification for insight generation
                NotificationCenter.default.post(name: .healthEntryUpdated, object: existingEntry)

                // Sync with Supabase - wrapped in a Task to avoid blocking the UI
                Task {
                    do {
                        try await syncManager.syncHealthEntry(entry: existingEntry)
                    } catch {
                        print("Error syncing updated health entry: \(error)")
                    }
                }

                return true
            } catch {
                print("Error updating health entry: \(error)")
                return false
            }
        } else {
            // Create new entry
            let entry = HealthEntry(
                timestamp: timestamp,
                type: healthEntryType,
                notes: notes.isEmpty ? nil : notes,
                baby: baby
            )

            // Set type-specific properties
            switch healthEntryType {
            case .temperature:
                if let temperatureValue = Double(temperature) {
                    entry.temperature = temperatureValue
                    entry.temperatureUnit = temperatureUnit
                }

            case .medication:
                entry.medicationName = medicationName
                entry.medicationDosage = medicationDosage.isEmpty ? nil : medicationDosage

            case .symptom:
                entry.setSymptoms(selectedSymptoms)

            case .vaccination:
                entry.vaccineName = vaccineName

            case .appointment:
                entry.appointmentReason = appointmentReason
                entry.appointmentProvider = appointmentProvider.isEmpty ? nil : appointmentProvider
            }

            modelContext.insert(entry)

            do {
                try modelContext.save()

                // Post notification for insight generation
                NotificationCenter.default.post(name: .healthEntryAdded, object: entry)

                // Sync with Supabase - wrapped in a Task to avoid blocking the UI
                Task {
                    do {
                        try await syncManager.syncHealthEntry(entry: entry)
                    } catch {
                        print("Error syncing health entry: \(error)")
                        // This is non-blocking, so we still return success for the local save
                    }
                }

                return true
            } catch {
                print("Error saving health entry: \(error)")
                return false
            }
        }
    }

    func saveGeneralActivityEntry(modelContext: ModelContext) -> Bool {
        if !validateGeneralActivityEntry() { return false }

        let entryToSave: GeneralActivityEntry
        if let existingEntry = entryToEdit as? GeneralActivityEntry, isEditMode {
            entryToSave = existingEntry
            entryToSave.timestamp = timestamp
            entryToSave.notes = notes.isEmpty ? nil : notes
            entryToSave.activityType = generalActivityType
            entryToSave.customActivityName = generalActivityType == .other ? (customGeneralActivityName.isEmpty ? nil : customGeneralActivityName) : nil
            entryToSave.durationMinutes = generalActivityDurationMinutes == 0 ? nil : generalActivityDurationMinutes
            entryToSave.updatedAt = Date()
            entryToSave.syncStatus = .notSynced // Mark for sync
            NotificationCenter.default.post(name: .generalActivityEntryUpdated, object: entryToSave)
        } else {
            entryToSave = GeneralActivityEntry(
                timestamp: timestamp,
                notes: notes.isEmpty ? nil : notes,
                baby: currentBaby!,
                activityType: generalActivityType,
                customActivityName: generalActivityType == .other ? (customGeneralActivityName.isEmpty ? nil : customGeneralActivityName) : nil,
                durationMinutes: generalActivityDurationMinutes == 0 ? nil : generalActivityDurationMinutes
            )
            modelContext.insert(entryToSave)
            NotificationCenter.default.post(name: .generalActivityAdded, object: entryToSave)
        }

        // Attempt to sync the entry
        // Note: SyncManager doesn't have a general saveEntry method, so we'll just log for now
        // In a real implementation, you would add a specific sync method for general activity entries
        print("Note: General activity entries are not currently synced to the server.")

        resetFormProperties()
        return true
    }

    func saveEntry(modelContext: ModelContext) -> Bool {
        guard let baby = currentBaby else {
            // Handle error: no current baby selected
            print("Error: No current baby selected for saving entry.")
            return false
        }

        guard let category = selectedCategory else {
            print("Error: No category selected for saving entry.")
            return false
        }

        switch category {
        case .feeding:
            return saveFeedingEntry(modelContext: modelContext)
        case .diaper:
            return saveDiaperEntry(modelContext: modelContext)
        case .sleep:
            return saveSleepEntry(modelContext: modelContext)
        case .growth:
            return saveGrowthEntry(modelContext: modelContext)
        case .health:
            return saveHealthEntry(modelContext: modelContext)
        case .general:
            return saveGeneralActivityEntry(modelContext: modelContext)
        }
    }
}
