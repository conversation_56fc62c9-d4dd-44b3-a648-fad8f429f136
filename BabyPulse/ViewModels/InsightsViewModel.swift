//
//  InsightsViewModel.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData
import Combine

class InsightsViewModel: ObservableObject {
    @Published var insights: [Insight] = []
    @Published var filteredInsights: [Insight] = []
    @Published var selectedCategory: Insight.InsightCategory? = nil
    @Published var timeRange: TimeRange = .daily
    @Published var sortOption: SortOption = .newest
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var selectedInsight: Insight?
    @Published var showingInsightDetail: Bool = false
    @Published var showingFilterSheet: Bool = false

    // Cancellables for observation
    private var cancellables = Set<AnyCancellable>()

    var modelContext: ModelContext?
    var currentBaby: Baby?
    private let insightGenerationManager = InsightGenerationManager.shared

    enum TimeRange: String, CaseIterable, Identifiable {
        case daily = "Daily"
        case weekly = "Weekly"
        case monthly = "Monthly"

        var id: String { self.rawValue }
    }

    enum SortOption: String, CaseIterable, Identifiable {
        case newest = "Newest"
        case oldest = "Oldest"
        case needsAttention = "Needs Attention"
        case highestConfidence = "Highest Confidence"
        case severity = "Severity"

        var id: String { self.rawValue }
    }

    init(modelContext: ModelContext? = nil) {
        self.modelContext = modelContext
        loadData()
    }

    func loadData() {
        isLoading = true
        errorMessage = nil

        Task { @MainActor in
            await loadCurrentBaby()
            applyFiltersAndSort()
            isLoading = false
        }
    }

    @MainActor
    private func loadCurrentBaby() async {
        // Load the current baby from UserPreferences
        guard let modelContext = modelContext else { return }

        do {
            // Use our safe method to fetch the selected baby
            if let baby = modelContext.fetchSelectedBaby() {
                // We have the current baby, now load existing insights
                await loadExistingInsights(for: baby)
            } else {
                // No selected baby or it's invalid
                // Check if we have any babies at all
                let babyDescriptor = FetchDescriptor<Baby>()
                let babies = try modelContext.fetch(babyDescriptor)

                if let firstBaby = babies.first {
                    // If we have at least one baby, select it and load existing insights
                    modelContext.updateSelectedBabyID(firstBaby.id)
                    await loadExistingInsights(for: firstBaby)
                } else {
                    // No babies at all
                    currentBaby = nil
                    insights = []
                    filteredInsights = []
                }
            }
        } catch {
            errorMessage = "Failed to load baby: \(error.localizedDescription)"
        }
    }

    @MainActor
    private func loadExistingInsights(for baby: Baby) async {
        guard let modelContext = modelContext else { return }

        // Verify the baby is still valid before proceeding
        guard baby.isValid else {
            // Baby has been invalidated, reload current baby
            await loadCurrentBaby()
            return
        }

        // Store the current baby for later use
        self.currentBaby = baby

        // Fetch existing insights from the database
        do {
            // Create a descriptor without predicate first
            var descriptor = FetchDescriptor<Insight>()
            descriptor.sortBy = [SortDescriptor(\Insight.timestamp, order: .reverse)]

            // Fetch all insights
            let allInsights = try modelContext.fetch(descriptor)

            // Then filter manually
            let babyId = baby.id
            let fetchedInsights = allInsights.filter { insight in
                guard let insightBaby = insight.baby else { return false }
                return insightBaby.id == babyId
            }

            self.insights = fetchedInsights
            self.applyFiltersAndSort()
        } catch {
            errorMessage = "Failed to load insights: \(error.localizedDescription)"
        }
    }

    /// Manually trigger insight generation
    func manuallyRefreshInsights() {
        Task { @MainActor in
            isLoading = true
            errorMessage = nil

            guard let baby = currentBaby else {
                isLoading = false
                errorMessage = "No baby selected"
                return
            }

            // Verify the baby is still valid before proceeding
            guard baby.isValid else {
                // Baby has been invalidated, reload current baby
                await loadCurrentBaby()
                isLoading = false
                return
            }

            // Use the InsightGenerationManager to generate insights
            insightGenerationManager.manuallyGenerateInsights(for: baby) { [weak self] newInsights in
                Task { @MainActor in
                    guard let self = self else { return }

                    // Verify the baby is still valid
                    guard baby.isValid else {
                        // Baby has been invalidated, reload data
                        await self.loadCurrentBaby()
                        self.isLoading = false
                        return
                    }

                    if !newInsights.isEmpty {
                        // Fetch all insights again to get the updated list
                        do {
                            guard let modelContext = self.modelContext else { return }

                            // Create a descriptor without predicate first
                            var descriptor = FetchDescriptor<Insight>()
                            descriptor.sortBy = [SortDescriptor(\Insight.timestamp, order: .reverse)]

                            // Fetch all insights
                            let allInsights = try modelContext.fetch(descriptor)

                            // Then filter manually
                            let babyId = baby.id
                            let fetchedInsights = allInsights.filter { insight in
                                guard let insightBaby = insight.baby else { return false }
                                return insightBaby.id == babyId
                            }

                            self.insights = fetchedInsights
                            self.applyFiltersAndSort()
                        } catch {
                            self.errorMessage = "Failed to refresh insights: \(error.localizedDescription)"
                        }
                    }

                    self.isLoading = false
                }
            }
        }
    }

    @MainActor
    func applyFiltersAndSort() {
        // Filter insights by selected category
        var filtered = insights

        if let selectedCategory = selectedCategory {
            filtered = insights.filter { insight in
                insight.category == selectedCategory
            }
        }

        // Filter out duplicate insights based on hashKey
        filtered = filterDuplicateInsights(filtered)

        // Filter by time range
        let calendar = Calendar.current
        let now = Date()

        switch timeRange {
        case .daily:
            // Filter for today's insights
            filtered = filtered.filter { insight in
                calendar.isDateInToday(insight.timestamp)
            }
        case .weekly:
            // Filter for this week's insights
            filtered = filtered.filter { insight in
                let components = calendar.dateComponents([.weekOfYear, .yearForWeekOfYear], from: insight.timestamp)
                let currentComponents = calendar.dateComponents([.weekOfYear, .yearForWeekOfYear], from: now)
                return components.weekOfYear == currentComponents.weekOfYear &&
                       components.yearForWeekOfYear == currentComponents.yearForWeekOfYear
            }
        case .monthly:
            // Filter for this month's insights
            filtered = filtered.filter { insight in
                let components = calendar.dateComponents([.month, .year], from: insight.timestamp)
                let currentComponents = calendar.dateComponents([.month, .year], from: now)
                return components.month == currentComponents.month &&
                       components.year == currentComponents.year
            }
        }

        // Sort the filtered insights
        switch sortOption {
        case .newest:
            filtered.sort { $0.timestamp > $1.timestamp }
        case .oldest:
            filtered.sort { $0.timestamp < $1.timestamp }
        case .needsAttention:
            filtered.sort { ($0.needsAttention ? 1 : 0) > ($1.needsAttention ? 1 : 0) }
        case .highestConfidence:
            filtered.sort { $0.confidence > $1.confidence }
        case .severity:
            // Sort by severity (urgent -> warning -> info)
            filtered.sort { insight1, insight2 in
                let severityValue: [Insight.InsightSeverity: Int] = [
                    .urgent: 3,
                    .warning: 2,
                    .info: 1
                ]

                return (severityValue[insight1.severity] ?? 0) > (severityValue[insight2.severity] ?? 0)
            }
        }

        filteredInsights = filtered
    }

    func showInsightDetail(insight: Insight) {
        selectedInsight = insight
        showingInsightDetail = true
    }

    @MainActor
    func selectCategory(_ category: Insight.InsightCategory) {
        if selectedCategory == category {
            selectedCategory = nil
        } else {
            selectedCategory = category
        }

        applyFiltersAndSort()
    }

    @MainActor
    func setTimeRange(_ range: TimeRange) {
        timeRange = range
        applyFiltersAndSort()
    }

    /// Filter out duplicate insights based on hashKey
    private func filterDuplicateInsights(_ insights: [Insight]) -> [Insight] {
        // Group insights by hashKey
        let groupedInsights = Dictionary(grouping: insights) { $0.hashKey }

        // For each group, keep only the most recent insight
        return groupedInsights.compactMap { (_, insightsWithSameHash) -> Insight? in
            // If hashKey is empty, don't filter
            if insightsWithSameHash.first?.hashKey.isEmpty ?? true {
                return insightsWithSameHash.first
            }

            // Otherwise, return the most recent insight
            return insightsWithSameHash.sorted { $0.timestamp > $1.timestamp }.first
        }
    }

    /// Observe generation state changes from InsightGenerationManager
    func observeGenerationState(generationManager: InsightGenerationManager, onChange: @escaping (InsightGenerationManager.GenerationState) -> Void) {
        // Observe the generation state
        generationManager.objectWillChange
            .sink { [weak self] _ in
                guard let self = self else { return }

                // Get the current state
                let state = generationManager.generationState

                // Handle state changes
                DispatchQueue.main.async {
                    // Refresh insights when generation completes
                    if case .completed = state {
                        self.loadData()
                    }

                    // Call the onChange handler
                    onChange(state)
                }
            }
            .store(in: &cancellables)
    }
}


