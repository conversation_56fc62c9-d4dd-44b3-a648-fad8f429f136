import SwiftUI
import SwiftData
import ComposableArchitecture

// MARK: - Logs Feature
@Reducer
struct LogsFeature {
    @ObservableState
    struct State: Equatable {
        var selectedDate = Date()
        var logEntries: [TimelineEntry] = []
        var activeFilters: Set<LogCategory> = [.feeding, .diaper, .sleep, .growth, .health]
        var isLoading = false
        var errorMessage: String?
        var showingDatePicker = false
        
        // Chart state
        var chartState = ChartFeature.State()
        
        // Computed properties
        var formattedSelectedDate: String {
            let formatter = DateFormatter()
            formatter.dateFormat = "EEEE, MMMM d"
            return formatter.string(from: selectedDate)
        }
        
        var isToday: Bool {
            Calendar.current.isDateInToday(selectedDate)
        }
        
        var timeSlots: [(Int, [TimelineEntry])] {
            let entriesByHour = Dictionary(grouping: logEntries) { entry in
                Calendar.current.component(.hour, from: entry.timestamp)
            }
            return entriesByHour.sorted { $0.key > $1.key }
        }
    }
    
    enum Action: Equatable {
        case dateSelected(Date)
        case filterToggled(LogCategory)
        case loadEntries
        case entriesLoaded([TimelineEntry])
        case loadingFailed(String)
        case navigateDate(NavigationDirection)
        case editEntry(TimelineEntry)
        case chart(ChartFeature.Action)
        
        enum NavigationDirection: Equatable {
            case forward, backward
        }
    }
    
    @Dependency(\.modelContext) var modelContext
    
    var body: some Reducer<State, Action> {
        Scope(state: \.chartState, action: \.chart) {
            ChartFeature()
        }
        
        Reduce { state, action in
            switch action {
            case let .dateSelected(date):
                state.selectedDate = date
                return .send(.loadEntries)
                
            case let .filterToggled(category):
                if state.activeFilters.contains(category) {
                    state.activeFilters.remove(category)
                } else {
                    state.activeFilters.insert(category)
                }
                return .send(.loadEntries)
                
            case .loadEntries:
                state.isLoading = true
                state.errorMessage = nil
                return .run { [date = state.selectedDate, filters = state.activeFilters] send in
                    do {
                        let entries = try await loadEntriesForDate(date, filters: filters)
                        await send(.entriesLoaded(entries))
                    } catch {
                        await send(.loadingFailed(error.localizedDescription))
                    }
                }
                
            case let .entriesLoaded(entries):
                state.isLoading = false
                state.logEntries = entries
                return .none
                
            case let .loadingFailed(error):
                state.isLoading = false
                state.errorMessage = error
                return .none
                
            case let .navigateDate(direction):
                let calendar = Calendar.current
                switch direction {
                case .forward:
                    state.selectedDate = calendar.date(byAdding: .day, value: 1, to: state.selectedDate) ?? state.selectedDate
                case .backward:
                    state.selectedDate = calendar.date(byAdding: .day, value: -1, to: state.selectedDate) ?? state.selectedDate
                }
                return .send(.loadEntries)
                
            case let .editEntry(entry):
                // Handle entry editing
                return .none
                
            case .chart:
                return .none
            }
        }
    }
    
    private func loadEntriesForDate(_ date: Date, filters: Set<LogCategory>) async throws -> [TimelineEntry] {
        // Implementation for loading entries
        // This would use the modelContext dependency
        return []
    }
}

// MARK: - Chart Feature (Extracted from LogsViewModel)
@Reducer
struct ChartFeature {
    @ObservableState
    struct State: Equatable {
        var selectedTimeSpan: TimeSpan = .daily
        var selectedChartCategory: LogCategory?
        var feedingChartData: FeedingChartData?
        var sleepChartData: SleepChartData?
        var diaperChartData: DiaperChartData?
        var weightChartData: WeightChartData?
        var heightChartData: HeightChartData?
        var headCircumferenceChartData: HeadCircumferenceChartData?
        var categoryPatternData: [LogCategory: PatternData] = [:]
        var chartStartDate = Calendar.current.date(byAdding: .day, value: -6, to: Calendar.current.startOfDay(for: Date()))!
        var chartEndDate = Calendar.current.startOfDay(for: Date())
        var canNavigateChartForward = false
        
        var isAllChartDataEmpty: Bool {
            let feedingEmpty = feedingChartData?.dataPoints.isEmpty ?? true
            let sleepEmpty = sleepChartData?.dataPoints.isEmpty ?? true
            let diaperEmpty = diaperChartData?.dataPoints.isEmpty ?? true
            let weightEmpty = weightChartData?.dataPoints.isEmpty ?? true
            let heightEmpty = heightChartData?.dataPoints.isEmpty ?? true
            let headCircumferenceEmpty = headCircumferenceChartData?.dataPoints.isEmpty ?? true
            
            return feedingEmpty && sleepEmpty && diaperEmpty && weightEmpty && heightEmpty && headCircumferenceEmpty
        }
    }
    
    enum Action: Equatable {
        case timeSpanChanged(TimeSpan)
        case categorySelected(LogCategory?)
        case loadChartData
        case chartDataLoaded(ChartDataResponse)
        case navigateChart(ChartNavigationDirection)
        case resetChartPeriod
        
        enum ChartNavigationDirection: Equatable {
            case forward, backward
        }
    }
    
    var body: some Reducer<State, Action> {
        Reduce { state, action in
            switch action {
            case let .timeSpanChanged(timeSpan):
                state.selectedTimeSpan = timeSpan
                return .send(.loadChartData)
                
            case let .categorySelected(category):
                state.selectedChartCategory = category
                return .send(.loadChartData)
                
            case .loadChartData:
                return .run { [state] send in
                    let chartData = try await loadChartDataForTimeSpan(state.selectedTimeSpan)
                    await send(.chartDataLoaded(chartData))
                }
                
            case let .chartDataLoaded(response):
                state.feedingChartData = response.feedingData
                state.sleepChartData = response.sleepData
                state.diaperChartData = response.diaperData
                state.weightChartData = response.weightData
                state.heightChartData = response.heightData
                state.headCircumferenceChartData = response.headCircumferenceData
                return .none
                
            case let .navigateChart(direction):
                let calendar = Calendar.current
                let timeSpan = state.selectedTimeSpan
                let daysToAdd = timeSpan == .daily ? 1 : (timeSpan == .weekly ? 7 : 30)
                
                switch direction {
                case .forward:
                    state.chartStartDate = calendar.date(byAdding: .day, value: daysToAdd, to: state.chartStartDate) ?? state.chartStartDate
                    state.chartEndDate = calendar.date(byAdding: .day, value: daysToAdd, to: state.chartEndDate) ?? state.chartEndDate
                case .backward:
                    state.chartStartDate = calendar.date(byAdding: .day, value: -daysToAdd, to: state.chartStartDate) ?? state.chartStartDate
                    state.chartEndDate = calendar.date(byAdding: .day, value: -daysToAdd, to: state.chartEndDate) ?? state.chartEndDate
                }
                
                state.canNavigateChartForward = state.chartEndDate < Calendar.current.startOfDay(for: Date())
                return .send(.loadChartData)
                
            case .resetChartPeriod:
                state.chartStartDate = Calendar.current.date(byAdding: .day, value: -6, to: Calendar.current.startOfDay(for: Date()))!
                state.chartEndDate = Calendar.current.startOfDay(for: Date())
                state.canNavigateChartForward = false
                return .send(.loadChartData)
            }
        }
    }
    
    private func loadChartDataForTimeSpan(_ timeSpan: TimeSpan) async throws -> ChartDataResponse {
        // Implementation for loading chart data
        return ChartDataResponse()
    }
}

// MARK: - Supporting Types
struct ChartDataResponse: Equatable {
    var feedingData: FeedingChartData?
    var sleepData: SleepChartData?
    var diaperData: DiaperChartData?
    var weightData: WeightChartData?
    var heightData: HeightChartData?
    var headCircumferenceData: HeadCircumferenceChartData?
}

// MARK: - Summary Feature (Extracted summary logic)
@Reducer
struct SummaryFeature {
    @ObservableState
    struct State: Equatable {
        var logEntries: [TimelineEntry] = []
        
        var feedingSummaryText: String {
            let feedingEntries = logEntries.filter { $0.category == .feeding }
            return feedingEntries.isEmpty ? "" : "\(feedingEntries.count) feedings today"
        }
        
        var diaperSummaryText: String {
            let diaperEntries = logEntries.filter { $0.category == .diaper }
            return diaperEntries.isEmpty ? "" : "\(diaperEntries.count) changes today"
        }
        
        var sleepSummaryText: String {
            let sleepEntries = logEntries.filter { $0.category == .sleep }
            if sleepEntries.isEmpty { return "" }
            
            let totalMinutes = sleepEntries.compactMap { $0.duration }.reduce(0, +)
            if totalMinutes == 0 { return "\(sleepEntries.count) sleep periods" }
            
            let hours = totalMinutes / 60
            let minutes = totalMinutes % 60
            
            return hours > 0 ? "\(hours)h \(minutes)m total" : "\(minutes)m total"
        }
    }
    
    enum Action: Equatable {
        case updateEntries([TimelineEntry])
    }
    
    var body: some Reducer<State, Action> {
        Reduce { state, action in
            switch action {
            case let .updateEntries(entries):
                state.logEntries = entries
                return .none
            }
        }
    }
}

// MARK: - Dependency Extension
extension DependencyValues {
    var modelContext: ModelContext {
        get { self[ModelContextKey.self] }
        set { self[ModelContextKey.self] = newValue }
    }
}

private enum ModelContextKey: DependencyKey {
    static let liveValue = ModelContext(ModelContainer(for: Baby.self))
} 