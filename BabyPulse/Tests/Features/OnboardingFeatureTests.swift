import XCTest
import ComposableArchitecture
import PhotosUI
@testable import BabyPulse

@MainActor
final class OnboardingFeatureTests: XCTestCase {
    
    func testOnAppearAndInitialState() {
        let store = TestStore(initialState: OnboardingFeature.State()) {
            OnboardingFeature()
        }
        
        // Test initial state
        XCTAssertEqual(store.state.currentStep, .welcome)
        XCTAssertEqual(store.state.babyName, "")
        XCTAssertEqual(store.state.gender, .male)
        XCTAssertEqual(store.state.unitSystem, .metric)
        XCTAssertTrue(store.state.notificationsEnabled)
        XCTAssertFalse(store.state.darkModeEnabled)
        XCTAssertNil(store.state.selectedAvatarIndex)
        XCTAssertNil(store.state.nameError)
    }
    
    func testMoveToNextStep() async {
        let store = TestStore(initialState: OnboardingFeature.State()) {
            OnboardingFeature()
        }
        
        // From welcome to baby profile
        await store.send(.moveToNextStep) {
            $0.currentStep = .babyProfile
        }
        
        // Cannot move to next step without valid baby profile
        await store.send(.moveToNextStep)
        // No state change expected since baby name is empty
    }
    
    func testMoveToPreviousStep() async {
        let store = TestStore(initialState: OnboardingFeature.State(
            currentStep: .babyProfile
        )) {
            OnboardingFeature()
        }
        
        await store.send(.moveToPreviousStep) {
            $0.currentStep = .welcome
        }
        
        // Cannot move before welcome step
        await store.send(.moveToPreviousStep)
        // No state change expected
    }
    
    func testBabyNameValidation() async {
        let store = TestStore(initialState: OnboardingFeature.State()) {
            OnboardingFeature()
        }
        
        // Empty name should trigger validation error
        await store.send(.babyNameChanged("")) {
            $0.babyName = ""
        }
        
        await store.receive(.validateBabyProfile) {
            $0.nameError = "Please enter your baby's name"
        }
        
        // Valid name should clear error
        await store.send(.babyNameChanged("Alice")) {
            $0.babyName = "Alice"
        }
        
        await store.receive(.validateBabyProfile) {
            $0.nameError = nil
        }
    }
    
    func testBabyProfileInputs() async {
        let store = TestStore(initialState: OnboardingFeature.State()) {
            OnboardingFeature()
        }
        
        let testDate = Date()
        
        await store.send(.birthDateChanged(testDate)) {
            $0.birthDate = testDate
        }
        
        await store.send(.genderChanged(.female)) {
            $0.gender = .female
        }
        
        await store.send(.unitSystemChanged(.imperial)) {
            $0.unitSystem = .imperial
        }
        
        await store.send(.notificationsToggled) {
            $0.notificationsEnabled = false
        }
        
        await store.send(.darkModeToggled) {
            $0.darkModeEnabled = true
        }
    }
    
    func testAvatarSelection() async {
        let store = TestStore(initialState: OnboardingFeature.State()) {
            OnboardingFeature()
        }
        
        await store.send(.avatarSelected(2)) {
            $0.selectedAvatarIndex = 2
            $0.photoData = nil
            $0.selectedPhoto = nil
        }
    }
    
    func testPhotoSelection() async {
        let store = TestStore(initialState: OnboardingFeature.State()) {
            OnboardingFeature()
        }
        
        // Mock PhotosPickerItem
        let mockPhoto = MockPhotosPickerItem()
        
        await store.send(.photoSelected(mockPhoto)) {
            $0.selectedPhoto = mockPhoto
            $0.selectedAvatarIndex = nil
        }
        
        // Simulate photo data loading
        let testData = Data([1, 2, 3, 4])
        await store.receive(.photoDataLoaded(testData)) {
            $0.photoData = testData
        }
        
        // Test clearing photo
        await store.send(.photoSelected(nil)) {
            $0.selectedPhoto = nil
            $0.selectedAvatarIndex = nil
        }
        
        await store.receive(.photoDataLoaded(nil)) {
            $0.photoData = nil
        }
    }
    
    func testCompleteOnboarding() async {
        let store = TestStore(initialState: OnboardingFeature.State(
            babyName: "Test Baby",
            gender: .female,
            unitSystem: .imperial,
            notificationsEnabled: false,
            darkModeEnabled: true
        )) {
            OnboardingFeature()
        } withDependencies: {
            $0.modelContext = MockModelContext()
        }
        
        await store.send(.completeOnboarding) {
            $0.isCompletingOnboarding = true
            $0.errorMessage = nil
        }
        
        await store.receive(.onboardingCompleted) {
            $0.isCompletingOnboarding = false
        }
    }
    
    func testCompleteOnboardingFailure() async {
        let store = TestStore(initialState: OnboardingFeature.State(
            babyName: "Test Baby"
        )) {
            OnboardingFeature()
        } withDependencies: {
            $0.modelContext = FailingModelContext()
        }
        
        await store.send(.completeOnboarding) {
            $0.isCompletingOnboarding = true
            $0.errorMessage = nil
        }
        
        await store.receive(.onboardingFailed("Test error")) {
            $0.isCompletingOnboarding = false
            $0.errorMessage = "Test error"
        }
    }
    
    func testComputedProperties() {
        // Test canMoveToNextStep
        var state = OnboardingFeature.State()
        XCTAssertTrue(state.canMoveToNextStep) // Welcome step
        
        state.currentStep = .babyProfile
        XCTAssertFalse(state.canMoveToNextStep) // Empty name
        
        state.babyName = "Valid Name"
        XCTAssertTrue(state.canMoveToNextStep) // Valid profile
        
        state.nameError = "Some error"
        XCTAssertFalse(state.canMoveToNextStep) // Has error
        
        // Test isValidBabyProfile
        state.nameError = nil
        XCTAssertTrue(state.isValidBabyProfile)
        
        state.babyName = "   "
        XCTAssertFalse(state.isValidBabyProfile) // Whitespace only
        
        // Test babyAge computation
        let calendar = Calendar.current
        let birthDate = calendar.date(byAdding: .day, value: -30, to: Date())!
        state.birthDate = birthDate
        XCTAssertTrue(state.babyAge.contains("30 days") || state.babyAge.contains("1 month"))
        
        // Test avatarOptions
        XCTAssertEqual(state.avatarOptions.count, 4)
        XCTAssertTrue(state.avatarOptions.contains("baby.avatar.1"))
    }
    
    func testOnboardingStepProperties() {
        XCTAssertEqual(OnboardingStep.welcome.title, "Welcome to BabyPulse")
        XCTAssertEqual(OnboardingStep.babyProfile.title, "Create Baby Profile")
        
        XCTAssertEqual(OnboardingStep.welcome.subtitle, "Track your baby's activities and get personalized insights")
        XCTAssertEqual(OnboardingStep.babyProfile.subtitle, "Tell us about your little one")
    }
}

// MARK: - Sync Feature Tests
@MainActor
final class SyncFeatureTests: XCTestCase {
    
    func testOnAppear() async {
        let store = TestStore(initialState: SyncFeature.State()) {
            SyncFeature()
        }
        
        await store.send(.onAppear)
        await store.receive(.loadSyncInfo)
        await store.receive(.checkConnectivity)
    }
    
    func testLoadSyncInfo() async {
        let store = TestStore(initialState: SyncFeature.State()) {
            SyncFeature()
        } withDependencies: {
            $0.userDefaults = MockUserDefaults()
            $0.supabaseService = MockSupabaseService()
            $0.revenueCatService = MockRevenueCatService()
        }
        
        await store.send(.loadSyncInfo)
        
        await store.receive(.syncInfoLoaded(SyncInfo(
            autoSyncEnabled: false,
            syncInterval: 3600,
            lastSyncDate: nil,
            syncEnabled: true,
            hasPremiumAccess: true
        ))) {
            $0.autoSyncEnabled = false
            $0.syncInterval = 3600
            $0.lastSyncDate = nil
            $0.syncEnabled = true
            $0.hasPremiumAccess = true
        }
        
        await store.receive(.loadMetrics)
    }
    
    func testConnectivityChange() async {
        let store = TestStore(initialState: SyncFeature.State(
            isSyncing: true,
            isOnline: true
        )) {
            SyncFeature()
        }
        
        await store.send(.connectivityChanged(false)) {
            $0.isOnline = false
            $0.isSyncing = false
            $0.syncStatus = "Sync interrupted - offline"
            $0.errorMessage = "Network connection lost during sync"
        }
        
        await store.send(.connectivityChanged(true)) {
            $0.isOnline = true
        }
    }
    
    func testAutoSyncToggle() async {
        let store = TestStore(initialState: SyncFeature.State()) {
            SyncFeature()
        } withDependencies: {
            $0.userDefaults = MockUserDefaults()
        }
        
        await store.send(.autoSyncToggled) {
            $0.autoSyncEnabled = true
        }
        
        await store.send(.autoSyncToggled) {
            $0.autoSyncEnabled = false
        }
    }
    
    func testSyncIntervalChange() async {
        let store = TestStore(initialState: SyncFeature.State()) {
            SyncFeature()
        } withDependencies: {
            $0.userDefaults = MockUserDefaults()
        }
        
        await store.send(.syncIntervalChanged(7200)) {
            $0.syncInterval = 7200
        }
    }
    
    func testSyncNowSuccess() async {
        let store = TestStore(initialState: SyncFeature.State(
            isOnline: true,
            syncEnabled: true
        )) {
            SyncFeature()
        } withDependencies: {
            $0.userDefaults = MockUserDefaults()
            $0.date = .constant(Date())
            $0.continuousClock = TestClock()
        }
        
        await store.send(.syncNow)
        await store.receive(.syncStarted(.manual)) {
            $0.isSyncing = true
            $0.syncProgress = 0.0
            $0.errorMessage = nil
            $0.syncStatus = "Starting sync..."
        }
    }
    
    func testSyncNowOffline() async {
        let store = TestStore(initialState: SyncFeature.State(
            isOnline: false,
            syncEnabled: true
        )) {
            SyncFeature()
        }
        
        await store.send(.syncNow) {
            $0.errorMessage = "Cannot sync while offline. Please check your internet connection."
        }
    }
    
    func testForceRefresh() async {
        let store = TestStore(initialState: SyncFeature.State(
            isOnline: true,
            syncEnabled: true
        )) {
            SyncFeature()
        } withDependencies: {
            $0.userDefaults = MockUserDefaults()
            $0.date = .constant(Date())
            $0.continuousClock = TestClock()
        }
        
        await store.send(.forceRefresh)
        await store.receive(.syncStarted(.forceRefresh)) {
            $0.isSyncing = true
            $0.syncProgress = 0.0
            $0.errorMessage = nil
            $0.syncStatus = "Starting force refresh..."
        }
    }
    
    func testClearSyncLog() async {
        let store = TestStore(initialState: SyncFeature.State()) {
            SyncFeature()
        } withDependencies: {
            $0.userDefaults = MockUserDefaults()
            $0.date = .constant(Date())
            $0.continuousClock = TestClock()
        }
        
        await store.send(.clearSyncLog)
        await store.receive(.syncStarted(.clearLog)) {
            $0.isSyncing = true
            $0.syncProgress = 0.0
            $0.errorMessage = nil
            $0.syncStatus = "Clearing sync logs..."
        }
    }
    
    func testSyncProgressUpdate() async {
        let store = TestStore(initialState: SyncFeature.State()) {
            SyncFeature()
        }
        
        await store.send(.syncProgressUpdated(0.5, "Syncing data...")) {
            $0.syncProgress = 0.5
            $0.syncStatus = "Syncing data..."
        }
    }
    
    func testSyncCompleted() async {
        let testDate = Date()
        let store = TestStore(initialState: SyncFeature.State(
            isSyncing: true,
            syncProgress: 1.0
        )) {
            SyncFeature()
        } withDependencies: {
            $0.userDefaults = MockUserDefaults()
            $0.date = .constant(testDate)
        }
        
        await store.send(.syncCompleted(.manual)) {
            $0.isSyncing = false
            $0.syncProgress = nil
            $0.lastSyncDate = testDate
            $0.syncStatus = "Sync completed"
        }
        
        await store.receive(.loadMetrics)
    }
    
    func testSyncFailed() async {
        let store = TestStore(initialState: SyncFeature.State(
            isSyncing: true,
            syncProgress: 0.5
        )) {
            SyncFeature()
        }
        
        await store.send(.syncFailed(.manual, "Network error")) {
            $0.isSyncing = false
            $0.syncProgress = nil
            $0.errorMessage = "Sync failed: Network error"
            $0.syncStatus = "Sync failed"
        }
    }
    
    func testDismissError() async {
        let store = TestStore(initialState: SyncFeature.State(
            errorMessage: "Some error"
        )) {
            SyncFeature()
        }
        
        await store.send(.dismissError) {
            $0.errorMessage = nil
        }
    }
    
    func testComputedProperties() {
        let testDate = Date()
        let state = SyncFeature.State(
            isOnline: true,
            lastSyncDate: testDate,
            syncEnabled: true,
            profilesCount: 2,
            feedingsCount: 50,
            diapersCount: 30,
            sleepCount: 25,
            healthCount: 10
        )
        
        XCTAssertTrue(state.canSync)
        XCTAssertEqual(state.totalDataItems, 117)
        XCTAssertFalse(state.formattedLastSyncDate.isEmpty)
        XCTAssertFalse(state.timeSinceLastSync.isEmpty)
        
        // Test offline state
        var offlineState = state
        offlineState.isOnline = false
        XCTAssertFalse(offlineState.canSync)
        
        // Test syncing state
        var syncingState = state
        syncingState.isSyncing = true
        XCTAssertFalse(syncingState.canSync)
        
        // Test sync disabled state
        var disabledState = state
        disabledState.syncEnabled = false
        XCTAssertFalse(disabledState.canSync)
    }
    
    func testSyncOperationProperties() {
        XCTAssertEqual(SyncOperation.manual.displayName, "Sync")
        XCTAssertEqual(SyncOperation.forceRefresh.displayName, "Force refresh")
        XCTAssertEqual(SyncOperation.clearLog.displayName, "Clear logs")
        
        XCTAssertEqual(SyncOperation.manual.steps.count, 10)
        XCTAssertEqual(SyncOperation.forceRefresh.steps.count, 15)
        XCTAssertEqual(SyncOperation.clearLog.steps.count, 1)
    }
}

// MARK: - Mock Dependencies

class MockPhotosPickerItem: PhotosPickerItem {
    // Mock implementation
}

class MockModelContext: ModelContext {
    // Mock implementation
}

class FailingModelContext: ModelContext {
    // Mock implementation that throws errors
}

class MockUserDefaults: UserDefaults {
    private var storage: [String: Any] = [:]
    
    override func bool(forKey defaultName: String) -> Bool {
        return storage[defaultName] as? Bool ?? false
    }
    
    override func double(forKey defaultName: String) -> Double {
        return storage[defaultName] as? Double ?? 0.0
    }
    
    override func set(_ value: Any?, forKey defaultName: String) {
        storage[defaultName] = value
    }
}

class MockSupabaseService: SupabaseService {
    var isSyncEnabled = true
}

class MockRevenueCatService: RevenueCatService {
    func hasPremiumAccess() async -> Bool {
        return true
    }
} 