import XCTest
import Composable<PERSON>rchitecture
@testable import BabyPulse

@MainActor
final class LogsFeatureTests: XCTestCase {
    
    func testDateNavigation() async {
        let store = TestStore(initialState: LogsFeature.State()) {
            LogsFeature()
        }
        
        let initialDate = store.state.selectedDate
        
        // Test forward navigation
        await store.send(.navigateDate(.forward)) {
            $0.selectedDate = Calendar.current.date(byAdding: .day, value: 1, to: initialDate)!
        }
        
        await store.receive(.loadEntries) {
            $0.isLoading = true
            $0.errorMessage = nil
        }
        
        // Test backward navigation
        await store.send(.navigateDate(.backward)) {
            $0.selectedDate = initialDate
        }
        
        await store.receive(.loadEntries) {
            $0.isLoading = true
            $0.errorMessage = nil
        }
    }
    
    func testFilterToggling() async {
        let store = TestStore(initialState: LogsFeature.State()) {
            LogsFeature()
        }
        
        // Initially all filters are active
        XCTAssertEqual(store.state.activeFilters, [.feeding, .diaper, .sleep, .growth, .health])
        
        // Toggle feeding filter off
        await store.send(.filterToggled(.feeding)) {
            $0.activeFilters.remove(.feeding)
        }
        
        await store.receive(.loadEntries) {
            $0.isLoading = true
            $0.errorMessage = nil
        }
        
        // Toggle feeding filter back on
        await store.send(.filterToggled(.feeding)) {
            $0.activeFilters.insert(.feeding)
        }
        
        await store.receive(.loadEntries) {
            $0.isLoading = true
            $0.errorMessage = nil
        }
    }
    
    func testDateSelection() async {
        let store = TestStore(initialState: LogsFeature.State()) {
            LogsFeature()
        }
        
        let newDate = Date().addingTimeInterval(-86400) // Yesterday
        
        await store.send(.dateSelected(newDate)) {
            $0.selectedDate = newDate
        }
        
        await store.receive(.loadEntries) {
            $0.isLoading = true
            $0.errorMessage = nil
        }
    }
    
    func testLoadingStates() async {
        let store = TestStore(initialState: LogsFeature.State()) {
            LogsFeature()
        }
        
        // Test successful loading
        await store.send(.loadEntries) {
            $0.isLoading = true
            $0.errorMessage = nil
        }
        
        let mockEntries = [
            TimelineEntry(id: UUID(), timestamp: Date(), category: .feeding, title: "Feeding", subtitle: nil)
        ]
        
        await store.send(.entriesLoaded(mockEntries)) {
            $0.isLoading = false
            $0.logEntries = mockEntries
        }
        
        // Test error loading
        await store.send(.loadEntries) {
            $0.isLoading = true
            $0.errorMessage = nil
        }
        
        await store.send(.loadingFailed("Network error")) {
            $0.isLoading = false
            $0.errorMessage = "Network error"
        }
    }
    
    func testComputedProperties() async {
        let store = TestStore(initialState: LogsFeature.State()) {
            LogsFeature()
        }
        
        // Test isToday
        XCTAssertTrue(store.state.isToday)
        
        await store.send(.dateSelected(Date().addingTimeInterval(-86400))) {
            $0.selectedDate = Date().addingTimeInterval(-86400)
        }
        
        XCTAssertFalse(store.state.isToday)
        
        // Test timeSlots grouping
        let entries = [
            TimelineEntry(id: UUID(), timestamp: Calendar.current.date(bySettingHour: 9, minute: 0, second: 0, of: Date())!, category: .feeding, title: "Morning Feed", subtitle: nil),
            TimelineEntry(id: UUID(), timestamp: Calendar.current.date(bySettingHour: 9, minute: 30, second: 0, of: Date())!, category: .diaper, title: "Diaper Change", subtitle: nil),
            TimelineEntry(id: UUID(), timestamp: Calendar.current.date(bySettingHour: 12, minute: 0, second: 0, of: Date())!, category: .feeding, title: "Lunch Feed", subtitle: nil)
        ]
        
        await store.send(.entriesLoaded(entries)) {
            $0.logEntries = entries
        }
        
        let timeSlots = store.state.timeSlots
        XCTAssertEqual(timeSlots.count, 2) // 9 AM and 12 PM
        XCTAssertEqual(timeSlots.first?.0, 12) // Sorted in descending order
        XCTAssertEqual(timeSlots.first?.1.count, 1) // One entry at 12 PM
        XCTAssertEqual(timeSlots.last?.0, 9) // 9 AM slot
        XCTAssertEqual(timeSlots.last?.1.count, 2) // Two entries at 9 AM
    }
}

@MainActor
final class ChartFeatureTests: XCTestCase {
    
    func testTimeSpanChange() async {
        let store = TestStore(initialState: ChartFeature.State()) {
            ChartFeature()
        }
        
        await store.send(.timeSpanChanged(.weekly)) {
            $0.selectedTimeSpan = .weekly
        }
        
        await store.receive(.loadChartData)
        
        await store.send(.timeSpanChanged(.monthly)) {
            $0.selectedTimeSpan = .monthly
        }
        
        await store.receive(.loadChartData)
    }
    
    func testCategorySelection() async {
        let store = TestStore(initialState: ChartFeature.State()) {
            ChartFeature()
        }
        
        await store.send(.categorySelected(.feeding)) {
            $0.selectedChartCategory = .feeding
        }
        
        await store.receive(.loadChartData)
        
        await store.send(.categorySelected(nil)) {
            $0.selectedChartCategory = nil
        }
        
        await store.receive(.loadChartData)
    }
    
    func testChartNavigation() async {
        let store = TestStore(initialState: ChartFeature.State()) {
            ChartFeature()
        }
        
        let initialStartDate = store.state.chartStartDate
        let initialEndDate = store.state.chartEndDate
        
        // Test backward navigation (daily)
        await store.send(.navigateChart(.backward)) {
            $0.chartStartDate = Calendar.current.date(byAdding: .day, value: -1, to: initialStartDate)!
            $0.chartEndDate = Calendar.current.date(byAdding: .day, value: -1, to: initialEndDate)!
            $0.canNavigateChartForward = true
        }
        
        await store.receive(.loadChartData)
        
        // Test forward navigation
        await store.send(.navigateChart(.forward)) {
            $0.chartStartDate = initialStartDate
            $0.chartEndDate = initialEndDate
            $0.canNavigateChartForward = false
        }
        
        await store.receive(.loadChartData)
    }
    
    func testResetChartPeriod() async {
        let store = TestStore(initialState: ChartFeature.State()) {
            ChartFeature()
        }
        
        // Navigate to a different period first
        await store.send(.navigateChart(.backward))
        await store.receive(.loadChartData)
        
        // Reset to current period
        await store.send(.resetChartPeriod) {
            $0.chartStartDate = Calendar.current.date(byAdding: .day, value: -6, to: Calendar.current.startOfDay(for: Date()))!
            $0.chartEndDate = Calendar.current.startOfDay(for: Date())
            $0.canNavigateChartForward = false
        }
        
        await store.receive(.loadChartData)
    }
    
    func testIsAllChartDataEmpty() async {
        let store = TestStore(initialState: ChartFeature.State()) {
            ChartFeature()
        }
        
        // Initially all data should be empty
        XCTAssertTrue(store.state.isAllChartDataEmpty)
        
        // Add some chart data
        let chartData = ChartDataResponse(
            feedingData: FeedingChartData(dataPoints: [FeedingDataPoint(timestamp: Date(), volume: 100)]),
            sleepData: nil,
            diaperData: nil,
            weightData: nil,
            heightData: nil,
            headCircumferenceData: nil
        )
        
        await store.send(.chartDataLoaded(chartData)) {
            $0.feedingChartData = chartData.feedingData
        }
        
        XCTAssertFalse(store.state.isAllChartDataEmpty)
    }
}

@MainActor
final class SummaryFeatureTests: XCTestCase {
    
    func testSummaryCalculations() async {
        let store = TestStore(initialState: SummaryFeature.State()) {
            SummaryFeature()
        }
        
        let entries = [
            TimelineEntry(id: UUID(), timestamp: Date(), category: .feeding, title: "Feed 1", subtitle: nil),
            TimelineEntry(id: UUID(), timestamp: Date(), category: .feeding, title: "Feed 2", subtitle: nil),
            TimelineEntry(id: UUID(), timestamp: Date(), category: .diaper, title: "Diaper 1", subtitle: nil),
            TimelineEntry(id: UUID(), timestamp: Date(), category: .sleep, title: "Sleep 1", subtitle: nil, duration: 120), // 2 hours
            TimelineEntry(id: UUID(), timestamp: Date(), category: .sleep, title: "Sleep 2", subtitle: nil, duration: 90)   // 1.5 hours
        ]
        
        await store.send(.updateEntries(entries)) {
            $0.logEntries = entries
        }
        
        XCTAssertEqual(store.state.feedingSummaryText, "2 feedings today")
        XCTAssertEqual(store.state.diaperSummaryText, "1 changes today")
        XCTAssertEqual(store.state.sleepSummaryText, "3h 30m total")
    }
    
    func testEmptySummaries() async {
        let store = TestStore(initialState: SummaryFeature.State()) {
            SummaryFeature()
        }
        
        await store.send(.updateEntries([])) {
            $0.logEntries = []
        }
        
        XCTAssertEqual(store.state.feedingSummaryText, "")
        XCTAssertEqual(store.state.diaperSummaryText, "")
        XCTAssertEqual(store.state.sleepSummaryText, "")
    }
    
    func testSleepSummaryWithMinutesOnly() async {
        let store = TestStore(initialState: SummaryFeature.State()) {
            SummaryFeature()
        }
        
        let entries = [
            TimelineEntry(id: UUID(), timestamp: Date(), category: .sleep, title: "Short nap", subtitle: nil, duration: 45) // 45 minutes
        ]
        
        await store.send(.updateEntries(entries)) {
            $0.logEntries = entries
        }
        
        XCTAssertEqual(store.state.sleepSummaryText, "45m total")
    }
    
    func testSleepSummaryWithNoDuration() async {
        let store = TestStore(initialState: SummaryFeature.State()) {
            SummaryFeature()
        }
        
        let entries = [
            TimelineEntry(id: UUID(), timestamp: Date(), category: .sleep, title: "Sleep 1", subtitle: nil),
            TimelineEntry(id: UUID(), timestamp: Date(), category: .sleep, title: "Sleep 2", subtitle: nil)
        ]
        
        await store.send(.updateEntries(entries)) {
            $0.logEntries = entries
        }
        
        XCTAssertEqual(store.state.sleepSummaryText, "2 sleep periods")
    }
}

// MARK: - Mock Data Structures

// Note: Using the real TimelineEntry from Models

// Note: Using the real chart data types from Models 