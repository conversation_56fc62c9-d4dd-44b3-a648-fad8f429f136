import XCTest
import ComposableArchitecture
@testable import BabyPulse

@MainActor
final class LogEntryFeatureTests: XCTestCase {
    
    func testCategorySelection() async {
        let store = TestStore(initialState: LogEntryFeature.State()) {
            LogEntryFeature()
        }
        
        await store.send(.categorySelected(.feeding)) {
            $0.selectedCategory = .feeding
        }
        
        await store.send(.categorySelected(.diaper)) {
            $0.selectedCategory = .diaper
        }
    }
    
    func testTimestampChange() async {
        let store = TestStore(initialState: LogEntryFeature.State()) {
            LogEntryFeature()
        }
        
        let newTimestamp = Date().addingTimeInterval(-3600) // 1 hour ago
        
        await store.send(.timestampChanged(newTimestamp)) {
            $0.timestamp = newTimestamp
        }
    }
    
    func testNotesChange() async {
        let store = TestStore(initialState: LogEntryFeature.State()) {
            LogEntryFeature()
        }
        
        await store.send(.notesChanged("Test notes")) {
            $0.notes = "Test notes"
        }
    }
    
    func testValidationErrors() async {
        let store = TestStore(initialState: LogEntryFeature.State()) {
            LogEntryFeature()
        }
        
        // Set category to feeding and create validation error
        await store.send(.categorySelected(.feeding)) {
            $0.selectedCategory = .feeding
        }
        
        // Set bottle feeding type and empty volume (should create validation error)
        await store.send(.feeding(.feedingTypeChanged(.bottleFeeding))) {
            $0.feedingState.feedingType = .bottleFeeding
        }
        
        await store.receive(.feeding(.validateFields)) {
            $0.feedingState.volumeError = "Volume is required for bottle feeding"
        }
        
        // Now the main feature should report validation errors
        XCTAssertTrue(store.state.hasValidationErrors)
    }
    
    func testSaveEntryWithValidationErrors() async {
        let store = TestStore(initialState: LogEntryFeature.State()) {
            LogEntryFeature()
        } withDependencies: {
            $0.modelContext = MockModelContext()
        }
        
        // Set up state with validation errors
        await store.send(.categorySelected(.feeding))
        await store.send(.feeding(.feedingTypeChanged(.bottleFeeding)))
        await store.receive(.feeding(.validateFields))
        
        // Try to save - should not proceed due to validation errors
        await store.send(.saveEntry)
        // No effects should be triggered due to validation errors
    }
    
    func testCancelEntry() async {
        let store = TestStore(initialState: LogEntryFeature.State()) {
            LogEntryFeature()
        } withDependencies: {
            $0.dismiss = DismissEffect { }
        }
        
        await store.send(.cancelEntry)
        // Should trigger dismiss effect
    }
}

@MainActor
final class FeedingEntryFeatureTests: XCTestCase {
    
    func testFeedingTypeChange() async {
        let store = TestStore(initialState: FeedingEntryFeature.State()) {
            FeedingEntryFeature()
        }
        
        await store.send(.feedingTypeChanged(.bottleFeeding)) {
            $0.feedingType = .bottleFeeding
        }
        
        await store.receive(.validateFields) {
            $0.volumeError = "Volume is required for bottle feeding"
        }
    }
    
    func testBottleFeedingValidation() async {
        let store = TestStore(initialState: FeedingEntryFeature.State()) {
            FeedingEntryFeature()
        }
        
        // Set to bottle feeding
        await store.send(.feedingTypeChanged(.bottleFeeding)) {
            $0.feedingType = .bottleFeeding
        }
        
        await store.receive(.validateFields) {
            $0.volumeError = "Volume is required for bottle feeding"
        }
        
        // Add invalid volume
        await store.send(.volumeChanged("invalid")) {
            $0.volumeText = "invalid"
            $0.volume = nil
        }
        
        await store.receive(.validateFields) {
            $0.volumeError = "Please enter a valid volume"
        }
        
        // Add valid volume
        await store.send(.volumeChanged("120")) {
            $0.volumeText = "120"
            $0.volume = 120.0
        }
        
        await store.receive(.validateFields) {
            $0.volumeError = nil
        }
        
        XCTAssertFalse(store.state.hasValidationErrors)
    }
    
    func testSolidFoodValidation() async {
        let store = TestStore(initialState: FeedingEntryFeature.State()) {
            FeedingEntryFeature()
        }
        
        // Set to solid food
        await store.send(.feedingTypeChanged(.solidFood)) {
            $0.feedingType = .solidFood
        }
        
        await store.receive(.validateFields) {
            $0.foodItemError = "Food item is required"
        }
        
        // Add food item
        await store.send(.foodItemChanged("Banana")) {
            $0.foodItem = "Banana"
        }
        
        await store.receive(.validateFields) {
            $0.foodItemError = nil
        }
        
        XCTAssertFalse(store.state.hasValidationErrors)
    }
    
    func testBreastfeedingFields() async {
        let store = TestStore(initialState: FeedingEntryFeature.State()) {
            FeedingEntryFeature()
        }
        
        // Test duration change
        await store.send(.durationChanged(20)) {
            $0.duration = 20
        }
        
        // Test breast toggles
        await store.send(.leftBreastToggled) {
            $0.leftBreast = true
        }
        
        await store.send(.rightBreastToggled) {
            $0.rightBreast = true
        }
        
        await store.send(.leftBreastToggled) {
            $0.leftBreast = false
        }
    }
}

@MainActor
final class DiaperEntryFeatureTests: XCTestCase {
    
    func testDiaperTypeChange() async {
        let store = TestStore(initialState: DiaperEntryFeature.State()) {
            DiaperEntryFeature()
        }
        
        await store.send(.diaperTypeChanged(.dirty)) {
            $0.diaperType = .dirty
        }
        
        await store.send(.diaperTypeChanged(.mixed)) {
            $0.diaperType = .mixed
        }
    }
    
    func testPoopDetailsChange() async {
        let store = TestStore(initialState: DiaperEntryFeature.State()) {
            DiaperEntryFeature()
        }
        
        await store.send(.poopColorChanged(.brown)) {
            $0.poopColor = .brown
        }
        
        await store.send(.poopConsistencyChanged(.soft)) {
            $0.poopConsistency = .soft
        }
    }
    
    func testNoValidationErrors() async {
        let store = TestStore(initialState: DiaperEntryFeature.State()) {
            DiaperEntryFeature()
        }
        
        // Diaper entries should never have validation errors
        XCTAssertFalse(store.state.hasValidationErrors)
    }
}

@MainActor
final class SleepEntryFeatureTests: XCTestCase {
    
    func testTimeChanges() async {
        let store = TestStore(initialState: SleepEntryFeature.State()) {
            SleepEntryFeature()
        }
        
        let newStartTime = Date().addingTimeInterval(-3600) // 1 hour ago
        let newEndTime = Date() // Now
        
        await store.send(.startTimeChanged(newStartTime)) {
            $0.startTime = newStartTime
        }
        
        await store.send(.endTimeChanged(newEndTime)) {
            $0.endTime = newEndTime
        }
    }
    
    func testOngoingToggle() async {
        let store = TestStore(initialState: SleepEntryFeature.State()) {
            SleepEntryFeature()
        }
        
        await store.send(.ongoingToggled) {
            $0.isOngoing = true
        }
        
        await store.send(.ongoingToggled) {
            $0.isOngoing = false
        }
    }
    
    func testLocationChange() async {
        let store = TestStore(initialState: SleepEntryFeature.State()) {
            SleepEntryFeature()
        }
        
        await store.send(.locationChanged(.bassinet)) {
            $0.location = .bassinet
        }
    }
    
    func testValidationErrors() async {
        let store = TestStore(initialState: SleepEntryFeature.State()) {
            SleepEntryFeature()
        }
        
        // Set end time before start time
        let startTime = Date()
        let endTime = startTime.addingTimeInterval(-3600) // 1 hour before start
        
        await store.send(.startTimeChanged(startTime)) {
            $0.startTime = startTime
        }
        
        await store.send(.endTimeChanged(endTime)) {
            $0.endTime = endTime
        }
        
        // Should have validation error when not ongoing and end time <= start time
        XCTAssertTrue(store.state.hasValidationErrors)
        
        // Toggle to ongoing - should remove validation error
        await store.send(.ongoingToggled) {
            $0.isOngoing = true
        }
        
        XCTAssertFalse(store.state.hasValidationErrors)
    }
}

@MainActor
final class GrowthEntryFeatureTests: XCTestCase {
    
    func testMeasurementChanges() async {
        let store = TestStore(initialState: GrowthEntryFeature.State()) {
            GrowthEntryFeature()
        }
        
        await store.send(.weightChanged("5.2")) {
            $0.weightText = "5.2"
        }
        
        await store.receive(.validateFields)
        
        await store.send(.heightChanged("65.5")) {
            $0.heightText = "65.5"
        }
        
        await store.receive(.validateFields)
        
        await store.send(.headCircumferenceChanged("42.0")) {
            $0.headCircumferenceText = "42.0"
        }
        
        await store.receive(.validateFields)
        
        // Test computed values
        XCTAssertEqual(store.state.weightValue, 5.2)
        XCTAssertEqual(store.state.heightValue, 65.5)
        XCTAssertEqual(store.state.headCircumferenceValue, 42.0)
    }
    
    func testValidationErrors() async {
        let store = TestStore(initialState: GrowthEntryFeature.State()) {
            GrowthEntryFeature()
        }
        
        // Test invalid weight
        await store.send(.weightChanged("invalid")) {
            $0.weightText = "invalid"
        }
        
        await store.receive(.validateFields) {
            $0.weightError = "Please enter a valid weight"
        }
        
        // Test invalid height
        await store.send(.heightChanged("not_a_number")) {
            $0.heightText = "not_a_number"
        }
        
        await store.receive(.validateFields) {
            $0.heightError = "Please enter a valid height"
        }
        
        XCTAssertTrue(store.state.hasValidationErrors)
        
        // Fix the errors
        await store.send(.weightChanged("5.0")) {
            $0.weightText = "5.0"
        }
        
        await store.receive(.validateFields) {
            $0.weightError = nil
        }
        
        await store.send(.heightChanged("65.0")) {
            $0.heightText = "65.0"
        }
        
        await store.receive(.validateFields) {
            $0.heightError = nil
        }
        
        XCTAssertFalse(store.state.hasValidationErrors)
    }
}

@MainActor
final class HealthEntryFeatureTests: XCTestCase {
    
    func testEntryTypeChange() async {
        let store = TestStore(initialState: HealthEntryFeature.State()) {
            HealthEntryFeature()
        }
        
        await store.send(.entryTypeChanged(.medication)) {
            $0.entryType = .medication
        }
        
        await store.receive(.validateFields) {
            $0.medicationNameError = "Medication name is required"
        }
    }
    
    func testTemperatureValidation() async {
        let store = TestStore(initialState: HealthEntryFeature.State()) {
            HealthEntryFeature()
        }
        
        // Temperature is the default type, should require temperature
        await store.send(.validateFields) {
            $0.temperatureError = "Temperature is required"
        }
        
        // Add invalid temperature
        await store.send(.temperatureChanged("not_a_number")) {
            $0.temperatureText = "not_a_number"
        }
        
        await store.receive(.validateFields) {
            $0.temperatureError = "Please enter a valid temperature"
        }
        
        // Add valid temperature
        await store.send(.temperatureChanged("37.5")) {
            $0.temperatureText = "37.5"
        }
        
        await store.receive(.validateFields) {
            $0.temperatureError = nil
        }
        
        XCTAssertEqual(store.state.temperatureValue, 37.5)
        XCTAssertFalse(store.state.hasValidationErrors)
    }
    
    func testMedicationValidation() async {
        let store = TestStore(initialState: HealthEntryFeature.State()) {
            HealthEntryFeature()
        }
        
        await store.send(.entryTypeChanged(.medication)) {
            $0.entryType = .medication
        }
        
        await store.receive(.validateFields) {
            $0.medicationNameError = "Medication name is required"
        }
        
        await store.send(.medicationNameChanged("Tylenol")) {
            $0.medicationName = "Tylenol"
        }
        
        await store.receive(.validateFields) {
            $0.medicationNameError = nil
        }
        
        await store.send(.medicationDosageChanged("5ml")) {
            $0.medicationDosage = "5ml"
        }
        
        XCTAssertFalse(store.state.hasValidationErrors)
    }
    
    func testSymptomToggling() async {
        let store = TestStore(initialState: HealthEntryFeature.State()) {
            HealthEntryFeature()
        }
        
        await store.send(.entryTypeChanged(.symptoms)) {
            $0.entryType = .symptoms
        }
        
        await store.receive(.validateFields)
        
        // Toggle fever symptom on
        await store.send(.symptomToggled(.fever)) {
            $0.selectedSymptoms = [.fever]
        }
        
        // Toggle cough symptom on
        await store.send(.symptomToggled(.cough)) {
            $0.selectedSymptoms = [.fever, .cough]
        }
        
        // Toggle fever symptom off
        await store.send(.symptomToggled(.fever)) {
            $0.selectedSymptoms = [.cough]
        }
    }
    
    func testVaccinationValidation() async {
        let store = TestStore(initialState: HealthEntryFeature.State()) {
            HealthEntryFeature()
        }
        
        await store.send(.entryTypeChanged(.vaccination)) {
            $0.entryType = .vaccination
        }
        
        await store.receive(.validateFields) {
            $0.vaccineNameError = "Vaccine name is required"
        }
        
        await store.send(.vaccineNameChanged("MMR")) {
            $0.vaccineName = "MMR"
        }
        
        await store.receive(.validateFields) {
            $0.vaccineNameError = nil
        }
        
        XCTAssertFalse(store.state.hasValidationErrors)
    }
    
    func testAppointmentValidation() async {
        let store = TestStore(initialState: HealthEntryFeature.State()) {
            HealthEntryFeature()
        }
        
        await store.send(.entryTypeChanged(.appointment)) {
            $0.entryType = .appointment
        }
        
        await store.receive(.validateFields) {
            $0.appointmentReasonError = "Appointment reason is required"
        }
        
        await store.send(.appointmentReasonChanged("Checkup")) {
            $0.appointmentReason = "Checkup"
        }
        
        await store.receive(.validateFields) {
            $0.appointmentReasonError = nil
        }
        
        await store.send(.appointmentProviderChanged("Dr. Smith")) {
            $0.appointmentProvider = "Dr. Smith"
        }
        
        XCTAssertFalse(store.state.hasValidationErrors)
    }
}

@MainActor
final class GeneralActivityEntryFeatureTests: XCTestCase {
    
    func testActivityTypeChange() async {
        let store = TestStore(initialState: GeneralActivityEntryFeature.State()) {
            GeneralActivityEntryFeature()
        }
        
        await store.send(.activityTypeChanged(.playTime)) {
            $0.activityType = .playTime
        }
        
        await store.receive(.validateFields)
    }
    
    func testCustomActivityValidation() async {
        let store = TestStore(initialState: GeneralActivityEntryFeature.State()) {
            GeneralActivityEntryFeature()
        }
        
        await store.send(.activityTypeChanged(.other)) {
            $0.activityType = .other
        }
        
        await store.receive(.validateFields) {
            $0.customNameError = "Custom activity name is required"
        }
        
        await store.send(.customNameChanged("Swimming")) {
            $0.customName = "Swimming"
        }
        
        await store.receive(.validateFields) {
            $0.customNameError = nil
        }
        
        XCTAssertFalse(store.state.hasValidationErrors)
    }
    
    func testDurationChange() async {
        let store = TestStore(initialState: GeneralActivityEntryFeature.State()) {
            GeneralActivityEntryFeature()
        }
        
        await store.send(.durationChanged(30)) {
            $0.durationMinutes = 30
        }
    }
}

// MARK: - Mock Dependencies

class MockModelContext {
    func insert(_ object: Any) {
        // Mock implementation
    }
    
    func save() throws {
        // Mock implementation
    }
}

struct DismissEffect {
    let action: () -> Void
    
    func callAsFunction() async {
        action()
    }
}

// MARK: - Mock Enums for Testing

// Note: Using the real GeneralActivityType from Models

extension LogCategory: CaseIterable {
    public static var allCases: [LogCategory] {
        [.feeding, .diaper, .sleep, .growth, .health]
    }
} 