import XCTest
import ComposableArchitecture
@testable import BabyPulse

@MainActor
final class HomeFeatureTests: XCTestCase {
    
    func testOnAppear() async {
        let store = TestStore(initialState: HomeFeature.State()) {
            HomeFeature()
        }
        
        await store.send(.onAppear) {
            $0.isLoading = true
            $0.errorMessage = nil
        }
    }
    
    func testLoadDataSuccess() async {
        let baby = Baby(name: "Test Baby", birthDate: Date().addingTimeInterval(-30 * 24 * 60 * 60), gender: .male)
        let summary = DailySummary()
        let insights: [Insight] = []
        
        let store = TestStore(initialState: HomeFeature.State()) {
            HomeFeature()
        } withDependencies: {
            $0.modelContext = MockModelContext()
            $0.date = .constant(Date())
            $0.calendar = Calendar.current
        }
        
        await store.send(.loadData) {
            $0.isLoading = true
            $0.errorMessage = nil
        }
        
        await store.receive(.dataLoaded(baby, summary, insights)) {
            $0.isLoading = false
            $0.currentBaby = baby
            $0.dailySummary = summary
            $0.insights = insights
        }
        
        await store.receive(.riskAssessed(.normal, "Everything looks good! Your baby's patterns are within normal ranges.")) {
            $0.riskStatus = .normal
            $0.riskMessage = "Everything looks good! Your baby's patterns are within normal ranges."
        }
    }
    
    func testLoadDataFailure() async {
        let store = TestStore(initialState: HomeFeature.State()) {
            HomeFeature()
        } withDependencies: {
            $0.modelContext = FailingModelContext()
        }
        
        await store.send(.loadData) {
            $0.isLoading = true
            $0.errorMessage = nil
        }
        
        await store.receive(.loadingFailed("Test error")) {
            $0.isLoading = false
            $0.errorMessage = "Test error"
        }
    }
    
    func testInsightSelection() async {
        let insight = Insight(
            title: "Test Insight",
            summary: "Test summary",
            content: "Test content",
            category: .feeding,
            severity: .info,
            confidence: 0.8,
            needsAttention: false,
            baby: nil
        )
        
        let store = TestStore(initialState: HomeFeature.State()) {
            HomeFeature()
        }
        
        await store.send(.insightSelected(insight)) {
            $0.selectedInsight = insight
            $0.showingInsightDetail = true
        }
        
        await store.send(.dismissInsightDetail) {
            $0.selectedInsight = nil
            $0.showingInsightDetail = false
        }
    }
    
    func testRiskAssessment() async {
        let store = TestStore(initialState: HomeFeature.State()) {
            HomeFeature()
        }
        
        await store.send(.riskAssessed(.warning, "Low diaper count today.")) {
            $0.riskStatus = .warning
            $0.riskMessage = "Low diaper count today."
        }
    }
    
    func testRefreshData() async {
        let store = TestStore(initialState: HomeFeature.State()) {
            HomeFeature()
        }
        
        await store.send(.refreshData) {
            $0.isLoading = true
            $0.errorMessage = nil
        }
    }
    
    func testSummaryCardsComputation() {
        let state = HomeFeature.State(
            dailySummary: DailySummary(
                totalFeedings: 8,
                totalDiapers: 6,
                totalSleepHours: 12.5
            )
        )
        
        let cards = state.summaryCards
        
        XCTAssertEqual(cards.count, 3)
        XCTAssertEqual(cards[0].title, "Feedings")
        XCTAssertEqual(cards[0].value, "8")
        XCTAssertEqual(cards[1].title, "Diapers")
        XCTAssertEqual(cards[1].value, "6")
        XCTAssertEqual(cards[2].title, "Sleep")
        XCTAssertEqual(cards[2].value, "12.5h")
    }
    
    func testHasCurrentBabyComputation() {
        var state = HomeFeature.State()
        XCTAssertFalse(state.hasCurrentBaby)
        
        state.currentBaby = Baby(name: "Test", birthDate: Date(), gender: .male)
        XCTAssertTrue(state.hasCurrentBaby)
    }
}

// MARK: - Insights Feature Tests
@MainActor
final class InsightsFeatureTests: XCTestCase {
    
    func testOnAppear() async {
        let store = TestStore(initialState: InsightsFeature.State()) {
            InsightsFeature()
        }
        
        await store.send(.onAppear)
        await store.receive(.loadData) {
            $0.isLoading = true
            $0.errorMessage = nil
        }
    }
    
    func testCategorySelection() async {
        let store = TestStore(initialState: InsightsFeature.State()) {
            InsightsFeature()
        }
        
        await store.send(.categorySelected(.feeding)) {
            $0.selectedCategory = .feeding
        }
        
        await store.receive(.applyFiltersAndSort)
        
        // Selecting same category again should deselect it
        await store.send(.categorySelected(.feeding)) {
            $0.selectedCategory = nil
        }
        
        await store.receive(.applyFiltersAndSort)
    }
    
    func testTimeRangeChange() async {
        let store = TestStore(initialState: InsightsFeature.State()) {
            InsightsFeature()
        }
        
        await store.send(.timeRangeChanged(.weekly)) {
            $0.timeRange = .weekly
        }
        
        await store.receive(.applyFiltersAndSort)
    }
    
    func testSortOptionChange() async {
        let store = TestStore(initialState: InsightsFeature.State()) {
            InsightsFeature()
        }
        
        await store.send(.sortOptionChanged(.highestConfidence)) {
            $0.sortOption = .highestConfidence
        }
        
        await store.receive(.applyFiltersAndSort)
    }
    
    func testFilterSheetPresentation() async {
        let store = TestStore(initialState: InsightsFeature.State()) {
            InsightsFeature()
        }
        
        await store.send(.showFilterSheet) {
            $0.showingFilterSheet = true
        }
        
        await store.send(.hideFilterSheet) {
            $0.showingFilterSheet = false
        }
    }
    
    func testInsightSelection() async {
        let insight = Insight(
            title: "Test Insight",
            summary: "Test summary",
            content: "Test content",
            category: .sleep,
            severity: .warning,
            confidence: 0.9,
            needsAttention: true,
            baby: nil
        )
        
        let store = TestStore(initialState: InsightsFeature.State()) {
            InsightsFeature()
        }
        
        await store.send(.insightSelected(insight)) {
            $0.selectedInsight = insight
            $0.showingInsightDetail = true
        }
        
        await store.send(.dismissInsightDetail) {
            $0.selectedInsight = nil
            $0.showingInsightDetail = false
        }
    }
    
    func testComputedProperties() {
        let insights = [
            Insight(title: "1", summary: "", content: "", category: .feeding, severity: .info, confidence: 0.8, needsAttention: true, baby: nil),
            Insight(title: "2", summary: "", content: "", category: .feeding, severity: .warning, confidence: 0.9, needsAttention: false, baby: nil),
            Insight(title: "3", summary: "", content: "", category: .sleep, severity: .info, confidence: 0.7, needsAttention: true, baby: nil)
        ]
        
        let state = InsightsFeature.State(
            insights: insights,
            filteredInsights: insights
        )
        
        XCTAssertTrue(state.hasInsights)
        XCTAssertEqual(state.needsAttentionCount, 2)
        XCTAssertEqual(state.categoryCounts[.feeding], 2)
        XCTAssertEqual(state.categoryCounts[.sleep], 1)
    }
}

// MARK: - Chat Feature Tests
@MainActor
final class ChatFeatureTests: XCTestCase {
    
    func testOnAppear() async {
        let store = TestStore(initialState: ChatFeature.State()) {
            ChatFeature()
        }
        
        await store.send(.onAppear)
        await store.receive(.loadMessages)
        await store.receive(.generateSuggestedQuestions)
    }
    
    func testInputTextChange() async {
        let store = TestStore(initialState: ChatFeature.State()) {
            ChatFeature()
        }
        
        await store.send(.inputTextChanged("Hello")) {
            $0.inputText = "Hello"
        }
    }
    
    func testCanSendMessageComputation() {
        var state = ChatFeature.State()
        XCTAssertFalse(state.canSendMessage)
        
        state.inputText = "Hello"
        XCTAssertTrue(state.canSendMessage)
        
        state.isLoading = true
        XCTAssertFalse(state.canSendMessage)
        
        state.isLoading = false
        state.isStreaming = true
        XCTAssertFalse(state.canSendMessage)
    }
    
    func testSuggestedQuestionTap() async {
        let store = TestStore(initialState: ChatFeature.State()) {
            ChatFeature()
        }
        
        await store.send(.suggestedQuestionTapped("How often should my baby feed?")) {
            $0.inputText = "How often should my baby feed?"
        }
    }
    
    func testStreamingFlow() async {
        let thread = ChatThread(baby: Baby(name: "Test", birthDate: Date(), gender: .male))
        
        let store = TestStore(initialState: ChatFeature.State(thread: thread)) {
            ChatFeature()
        }
        
        await store.send(.streamingStarted) {
            $0.isLoading = false
            $0.isStreaming = true
        }
        
        await store.send(.streamingTokenReceived("Hello")) {
            // Token should be added to the last message
        }
        
        await store.send(.streamingEnded) {
            $0.isStreaming = false
        }
        
        await store.receive(.generateSuggestedQuestions)
    }
    
    func testHasMessagesComputation() {
        var state = ChatFeature.State()
        XCTAssertFalse(state.hasMessages)
        
        let message = ChatMessage.createUserMessage(
            content: "Test",
            thread: ChatThread(baby: Baby(name: "Test", birthDate: Date(), gender: .male))
        )
        state.messages = [message]
        XCTAssertTrue(state.hasMessages)
        XCTAssertEqual(state.lastMessage?.content, "Test")
    }
}

// MARK: - Settings Feature Tests
@MainActor
final class SettingsFeatureTests: XCTestCase {
    
    func testOnAppear() async {
        let store = TestStore(initialState: SettingsFeature.State()) {
            SettingsFeature()
        }
        
        await store.send(.onAppear) {
            $0.isLoading = true
            $0.errorMessage = nil
        }
    }
    
    func testBabySelection() async {
        let babyId = UUID()
        
        let store = TestStore(initialState: SettingsFeature.State()) {
            SettingsFeature()
        } withDependencies: {
            $0.modelContext = MockModelContext()
        }
        
        await store.send(.babySelected(babyId)) {
            $0.selectedBabyID = babyId
        }
    }
    
    func testStartEditingBaby() async {
        let baby = Baby(name: "Test Baby", birthDate: Date(), gender: .female)
        
        let store = TestStore(initialState: SettingsFeature.State()) {
            SettingsFeature()
        }
        
        await store.send(.startEditingBaby(baby)) {
            $0.editingBaby = baby
            $0.tempBabyName = "Test Baby"
            $0.tempBabyBirthDate = baby.birthDate
            $0.tempBabyGender = .female
            $0.tempPhotoData = baby.photoData
            $0.tempSelectedPhoto = nil
            $0.nameError = nil
            $0.showingBabyProfileEdit = true
        }
    }
    
    func testStartAddingBaby() async {
        let store = TestStore(initialState: SettingsFeature.State()) {
            SettingsFeature()
        }
        
        await store.send(.startAddingBaby) {
            $0.editingBaby = nil
            $0.tempBabyName = ""
            $0.tempBabyBirthDate = Date()
            $0.tempBabyGender = .male
            $0.tempPhotoData = nil
            $0.tempSelectedPhoto = nil
            $0.nameError = nil
            $0.showingAddBabyProfile = true
        }
    }
    
    func testCancelBabyEdit() async {
        let store = TestStore(initialState: SettingsFeature.State(
            showingBabyProfileEdit: true,
            editingBaby: Baby(name: "Test", birthDate: Date(), gender: .male)
        )) {
            SettingsFeature()
        }
        
        await store.send(.cancelBabyEdit) {
            $0.showingBabyProfileEdit = false
            $0.showingAddBabyProfile = false
            $0.editingBaby = nil
            $0.nameError = nil
        }
    }
    
    func testTempBabyNameValidation() async {
        let store = TestStore(initialState: SettingsFeature.State()) {
            SettingsFeature()
        }
        
        await store.send(.tempBabyNameChanged("A")) {
            $0.tempBabyName = "A"
            $0.nameError = "Name must be at least 2 characters"
        }
        
        await store.send(.tempBabyNameChanged("Alice")) {
            $0.tempBabyName = "Alice"
            $0.nameError = nil
        }
        
        await store.send(.tempBabyNameChanged("")) {
            $0.tempBabyName = ""
            $0.nameError = "Name is required"
        }
    }
    
    func testSettingsToggles() async {
        let store = TestStore(initialState: SettingsFeature.State()) {
            SettingsFeature()
        } withDependencies: {
            $0.modelContext = MockModelContext()
        }
        
        await store.send(.darkModeToggled) {
            $0.darkModeEnabled = true
        }
        
        await store.send(.notificationsToggled) {
            $0.notificationsEnabled = false
        }
        
        await store.send(.analyticsToggled) {
            $0.analyticsEnabled = false
        }
        
        await store.send(.autoSyncToggled) {
            $0.autoSyncEnabled = true
        }
    }
    
    func testUnitSystemChange() async {
        let store = TestStore(initialState: SettingsFeature.State()) {
            SettingsFeature()
        } withDependencies: {
            $0.modelContext = MockModelContext()
        }
        
        await store.send(.unitSystemChanged(.imperial)) {
            $0.unitSystem = .imperial
        }
    }
    
    func testExportData() async {
        let store = TestStore(initialState: SettingsFeature.State()) {
            SettingsFeature()
        } withDependencies: {
            $0.modelContext = MockModelContext()
        }
        
        await store.send(.exportData) {
            $0.isExporting = true
        }
        
        await store.receive(.exportCompleted) {
            $0.isExporting = false
        }
    }
    
    func testComputedProperties() {
        let baby1 = Baby(name: "Baby 1", birthDate: Date(), gender: .male)
        let baby2 = Baby(name: "Baby 2", birthDate: Date(), gender: .female)
        
        let state = SettingsFeature.State(
            babies: [baby1, baby2],
            selectedBabyID: baby1.id,
            tempBabyName: "Valid Name"
        )
        
        XCTAssertEqual(state.selectedBaby?.id, baby1.id)
        XCTAssertTrue(state.hasMultipleBabies)
        XCTAssertTrue(state.canSaveBaby)
    }
    
    func testNotificationPermissionRequest() async {
        let store = TestStore(initialState: SettingsFeature.State()) {
            SettingsFeature()
        } withDependencies: {
            $0.userNotifications = MockUserNotificationCenter()
        }
        
        await store.send(.requestNotificationPermission)
        await store.receive(.notificationPermissionResult(true)) {
            $0.hasNotificationPermission = true
        }
    }
}

// MARK: - Mock Dependencies
class MockModelContext: ModelContext {
    // Mock implementation
}

class FailingModelContext: ModelContext {
    // Mock implementation that throws errors
}

class MockUserNotificationCenter: UNUserNotificationCenter {
    // Mock implementation
} 