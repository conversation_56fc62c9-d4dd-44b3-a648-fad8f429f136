//
//  DailySummary.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftUI

// MARK: - Daily Summary Model

struct DailySummary: Equatable {
    // Feeding metrics
    var totalFeedings: Int = 0
    var breastFeedings: Int = 0
    var bottleFeedings: Int = 0
    var solidFeedings: Int = 0
    var totalFeedingVolume: Double = 0.0
    
    // Diaper metrics
    var totalDiapers: Int = 0
    var wetDiapers: Int = 0
    var dirtyDiapers: Int = 0
    var mixedDiapers: Int = 0
    
    // Sleep metrics
    var totalSleepHours: Double = 0.0
    var sleepSessions: Int = 0
}

// MARK: - Risk Status Model

enum RiskStatus: Equatable {
    case normal
    case warning
    case alert
    
    var color: Color {
        switch self {
        case .normal: return .green
        case .warning: return .orange
        case .alert: return .red
        }
    }
    
    var icon: String {
        switch self {
        case .normal: return "checkmark.circle.fill"
        case .warning: return "exclamationmark.triangle.fill"
        case .alert: return "exclamationmark.octagon.fill"
        }
    }
}

// MARK: - Summary Card Model

struct SummaryCard: Equatable, Identifiable {
    let id = UUID()
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let color: Color
}
