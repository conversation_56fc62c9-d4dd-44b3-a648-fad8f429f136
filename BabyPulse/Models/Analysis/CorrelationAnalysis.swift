//
//  CorrelationAnalysis.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Represents correlation analysis between different categories
struct CorrelationAnalysis {
    /// The primary category being analyzed
    let primaryCategory: Insight.InsightCategory

    /// The secondary category being correlated with
    let secondaryCategory: Insight.InsightCategory

    /// The correlation strength (-1.0 to 1.0)
    let correlationStrength: Double

    /// The lag period between correlated events, if any
    let lagPeriod: TimeInterval?

    /// Human-readable description of the correlation
    let description: String

    /// Whether this correlation exceeds thresholds for generating an insight
    let exceedsThreshold: Bool

    /// Variables to be used in prompt templates
    var templateVariables: [String: String] {
        var variables: [String: String] = [
            "primary_category": primaryCategory.displayName,
            "secondary_category": secondaryCategory.displayName,
            "correlation_strength": String(format: "%.0f%%", abs(correlationStrength) * 100),
            "correlation_direction": correlationStrength > 0 ? "positive" : "negative",
            "correlation_description": description
        ]

        if let lagPeriod = lagPeriod {
            variables["lag_period"] = formatTimeInterval(lagPeriod)
        } else {
            variables["lag_period"] = "immediate"
        }

        return variables
    }

    /// Format a time interval in a human-readable way
    /// - Parameter interval: The time interval to format
    /// - Returns: A human-readable string representation of the interval
    private func formatTimeInterval(_ interval: TimeInterval) -> String {
        let hours = Int(interval / 3600)
        let minutes = Int((interval.truncatingRemainder(dividingBy: 3600)) / 60)

        if hours > 0 {
            if minutes > 0 {
                return "\(hours) hour\(hours == 1 ? "" : "s") and \(minutes) minute\(minutes == 1 ? "" : "s")"
            } else {
                return "\(hours) hour\(hours == 1 ? "" : "s")"
            }
        } else {
            return "\(minutes) minute\(minutes == 1 ? "" : "s")"
        }
    }
}
