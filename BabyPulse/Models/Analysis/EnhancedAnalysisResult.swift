//
//  EnhancedAnalysisResult.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Enhanced version of AnalysisResult with additional properties
struct EnhancedAnalysisResult {
    /// The original analysis result
    let baseResult: AnalysisResult

    /// The timeframe of the analysis
    let timeframe: AnalysisTimeframe

    /// Pattern detection results
    let patternResults: [PatternResult]

    /// Cyclical pattern detection results
    let cyclicalPatterns: [CyclicalPattern]

    /// Trend analysis results
    let trendAnalyses: [TrendAnalysis]

    /// Anomaly detection results
    let anomalyDetections: [AnomalyDetection]

    /// Correlation analyses
    let correlations: [CorrelationAnalysis]

    /// Developmental context
    let developmentalContext: DevelopmentalContext?

    /// Milestone proximity information
    let milestoneProximity: [(milestone: String, proximity: Double, indicators: [String])]

    /// Predictions based on the analysis
    let predictions: [Prediction]

    /// Enhanced template variables combining all sources
    var enhancedTemplateVariables: [String: String] {
        var variables = baseResult.templateVariables

        // Add timeframe context
        variables["timeframe"] = timeframe.description

        // Add pattern information
        if let primaryPattern = patternResults.first {
            variables["primary_pattern"] = primaryPattern.description
            variables["pattern_confidence"] = "\(primaryPattern.confidence)%"

            // Add pattern-specific variables
            for (key, value) in primaryPattern.templateVariables {
                variables["pattern_\(key)"] = value
            }
        }

        // Add cyclical pattern information
        if let primaryCyclicalPattern = cyclicalPatterns.first {
            variables["cyclical_pattern"] = primaryCyclicalPattern.description

            // Add cyclical pattern-specific variables
            for (key, value) in primaryCyclicalPattern.templateVariables {
                variables["cyclical_\(key)"] = value
            }
        }

        // Add trend analysis information
        if let primaryTrend = trendAnalyses.first {
            variables["trend_description"] = primaryTrend.description

            // Add trend-specific variables
            for (key, value) in primaryTrend.templateVariables {
                variables[key] = value
            }
        }

        // Add anomaly detection information
        if let primaryAnomaly = anomalyDetections.first {
            variables["anomaly_description"] = primaryAnomaly.description

            // Add anomaly-specific variables
            for (key, value) in primaryAnomaly.templateVariables {
                variables[key] = value
            }
        }

        // Add correlation information
        if let primaryCorrelation = correlations.first {
            for (key, value) in primaryCorrelation.templateVariables {
                variables["correlation_\(key)"] = value
            }
        }

        // Add developmental context
        if let context = developmentalContext {
            for (key, value) in context.templateVariables {
                variables[key] = value
            }
        }

        // Add milestone proximity information
        if let (milestone, proximity, indicators) = milestoneProximity.first {
            variables["milestone_name"] = milestone
            variables["milestone_proximity"] = String(format: "%.0f%%", proximity * 100)

            if !indicators.isEmpty {
                variables["milestone_indicators"] = indicators.joined(separator: ", ")
                variables["milestone_primary_indicator"] = indicators.first ?? ""
            }
        }

        // Add prediction information
        if let primaryPrediction = predictions.first {
            variables["prediction"] = primaryPrediction.description
            variables["prediction_confidence"] = "\(primaryPrediction.confidence)%"

            // Format the timeframe
            let formatter = DateFormatter()
            formatter.dateStyle = .short
            formatter.timeStyle = .short
            let timeframeDescription = "\(formatter.string(from: primaryPrediction.timeframe.start)) to \(formatter.string(from: primaryPrediction.timeframe.end))"

            variables["prediction_timeframe"] = timeframeDescription
        }

        return variables
    }

    /// Convert to a standard AnalysisResult
    func toAnalysisResult() -> AnalysisResult {
        return AnalysisResult(
            insightType: baseResult.insightType,
            metrics: baseResult.metrics,
            exceedsThreshold: baseResult.exceedsThreshold,
            templateVariables: enhancedTemplateVariables,
            category: baseResult.category,
            title: baseResult.title,
            metricString: baseResult.metricString,
            needsAttention: baseResult.needsAttention,
            confidence: baseResult.confidence
        )
    }
}

// Prediction struct is defined in BabyPulse/Models/Prediction/Prediction.swift
