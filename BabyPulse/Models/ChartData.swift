//
//  ChartData.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftUI

/// Base protocol for chart data
protocol ChartDataProtocol {
    var timeSpan: TimeSpan { get }
    var startDate: Date { get }
    var endDate: Date { get }
}

/// Generic chart data point
struct ChartDataPoint: Identifiable, Equatable {
    let id = UUID()
    let date: Date
    let value: Double
    let category: LogCategory

    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"  // Just day number for all days
        return formatter.string(from: date)
    }

    // Additional property to check if this is a significant date (start of week/month)
    var isSignificantDate: Bool {
        let calendar = Calendar.current
        let isStartOfWeek = calendar.component(.weekday, from: date) == calendar.firstWeekday
        let isStartOfMonth = calendar.component(.day, from: date) == 1
        return isStartOfMonth || isStartOfWeek
    }

    var formattedValue: String {
        String(format: "%.0f", value)
    }

    // Implement Equatable
    static func == (lhs: ChartDataPoint, rhs: ChartDataPoint) -> Bool {
        lhs.id == rhs.id &&
        lhs.date == rhs.date &&
        lhs.value == rhs.value &&
        lhs.category == rhs.category
    }
}

/// Feeding chart data
struct FeedingChartData: ChartDataProtocol, Equatable {
    let timeSpan: TimeSpan
    let startDate: Date
    let endDate: Date
    let dataPoints: [ChartDataPoint]
    let average: Double
    let minimum: Double
    let maximum: Double
    let percentChange: Double?

    var formattedAverage: String {
        String(format: "%.0f", average)
    }

    var formattedMinimum: String {
        String(format: "%.0f", minimum)
    }

    var formattedMaximum: String {
        String(format: "%.0f", maximum)
    }

    var formattedPercentChange: String {
        guard let percentChange = percentChange else { return "-" }
        return String(format: "%+.0f%%", percentChange)
    }

    var percentChangeColor: Color {
        guard let percentChange = percentChange else { return .gray }
        return percentChange >= 0 ? .green : .red
    }
}

/// Sleep chart data
struct SleepChartData: ChartDataProtocol, Equatable {
    let timeSpan: TimeSpan
    let startDate: Date
    let endDate: Date
    let dataPoints: [ChartDataPoint]
    let average: Double
    let minimum: Double
    let maximum: Double
    let percentChange: Double?

    var formattedAverage: String {
        String(format: "%.1f", average)
    }

    var formattedMinimum: String {
        String(format: "%.1f", minimum)
    }

    var formattedMaximum: String {
        String(format: "%.1f", maximum)
    }

    var formattedPercentChange: String {
        guard let percentChange = percentChange else { return "-" }
        return String(format: "%+.0f%%", percentChange)
    }

    var percentChangeColor: Color {
        guard let percentChange = percentChange else { return .gray }
        return percentChange >= 0 ? .green : .red
    }
}

/// Diaper chart data
struct DiaperChartData: ChartDataProtocol, Equatable {
    let timeSpan: TimeSpan
    let startDate: Date
    let endDate: Date
    let dataPoints: [ChartDataPoint]
    let wetCount: Int
    let dirtyCount: Int
    let mixedCount: Int
    let percentChange: Double?

    var totalCount: Int {
        wetCount + dirtyCount + mixedCount
    }

    var average: Double {
        Double(totalCount) / Double(timeSpan.days)
    }

    var formattedAverage: String {
        String(format: "%.1f", average)
    }

    var formattedPercentChange: String {
        guard let percentChange = percentChange else { return "-" }
        return String(format: "%+.0f%%", percentChange)
    }

    var percentChangeColor: Color {
        guard let percentChange = percentChange else { return .gray }
        return percentChange >= 0 ? .green : .red
    }
}

/// Weight chart data
struct WeightChartData: ChartDataProtocol, Equatable {
    let timeSpan: TimeSpan
    let startDate: Date
    let endDate: Date
    let dataPoints: [ChartDataPoint]
    let average: Double
    let minimum: Double
    let maximum: Double
    let percentChange: Double?
    let dailyGainAverage: Double?  // Average daily weight gain

    var formattedAverage: String {
        String(format: "%.1f", average)
    }

    var formattedMinimum: String {
        String(format: "%.1f", minimum)
    }

    var formattedMaximum: String {
        String(format: "%.1f", maximum)
    }

    var formattedPercentChange: String {
        guard let percentChange = percentChange else { return "-" }
        return String(format: "%+.0f%%", percentChange)
    }

    var formattedDailyGain: String {
        guard let dailyGainAverage = dailyGainAverage else { return "-" }
        return String(format: "%.1f g/day", dailyGainAverage * 1000) // Convert kg to g
    }

    var percentChangeColor: Color {
        guard let percentChange = percentChange else { return .gray }
        return percentChange >= 0 ? .green : .red
    }
}

/// Height chart data
struct HeightChartData: ChartDataProtocol, Equatable {
    let timeSpan: TimeSpan
    let startDate: Date
    let endDate: Date
    let dataPoints: [ChartDataPoint]
    let average: Double
    let minimum: Double
    let maximum: Double
    let percentChange: Double?

    var formattedAverage: String {
        String(format: "%.1f", average)
    }

    var formattedMinimum: String {
        String(format: "%.1f", minimum)
    }

    var formattedMaximum: String {
        String(format: "%.1f", maximum)
    }

    var formattedPercentChange: String {
        guard let percentChange = percentChange else { return "-" }
        return String(format: "%+.0f%%", percentChange)
    }

    var percentChangeColor: Color {
        guard let percentChange = percentChange else { return .gray }
        return percentChange >= 0 ? .green : .red
    }
}

/// Head circumference chart data
struct HeadCircumferenceChartData: ChartDataProtocol, Equatable {
    let timeSpan: TimeSpan
    let startDate: Date
    let endDate: Date
    let dataPoints: [ChartDataPoint]
    let average: Double
    let minimum: Double
    let maximum: Double
    let percentChange: Double?

    var formattedAverage: String {
        String(format: "%.1f", average)
    }

    var formattedMinimum: String {
        String(format: "%.1f", minimum)
    }

    var formattedMaximum: String {
        String(format: "%.1f", maximum)
    }

    var formattedPercentChange: String {
        guard let percentChange = percentChange else { return "-" }
        return String(format: "%+.0f%%", percentChange)
    }

    var percentChangeColor: Color {
        guard let percentChange = percentChange else { return .gray }
        return percentChange >= 0 ? .green : .red
    }
}

/// Pattern data for time-of-day visualization
struct PatternData: ChartDataProtocol, Equatable {
    let timeSpan: TimeSpan
    let startDate: Date
    let endDate: Date
    let category: LogCategory  // Added category property
    let activities: [PatternActivity]

    // Computed property to get all unique days in the date range
    var days: [Date] {
        let calendar = Calendar.current
        var currentDate = calendar.startOfDay(for: startDate)
        let endDay = calendar.startOfDay(for: endDate)

        var days: [Date] = []
        while currentDate <= endDay {
            days.append(currentDate)
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }

        return days
    }

    struct PatternActivity: Identifiable, Equatable, Hashable {
        let id = UUID()
        let date: Date
        let startTime: Date
        let endTime: Date?
        let category: LogCategory
        let duration: TimeInterval?

        var hourOfDay: Int {
            Calendar.current.component(.hour, from: startTime)
        }

        var dayOfWeek: String {
            let formatter = DateFormatter()
            formatter.dateFormat = "E"
            return formatter.string(from: date)
        }

        // Implement Equatable
        static func == (lhs: PatternActivity, rhs: PatternActivity) -> Bool {
            lhs.id == rhs.id
        }

        // Implement Hashable
        func hash(into hasher: inout Hasher) {
            hasher.combine(id)
        }
    }
}
