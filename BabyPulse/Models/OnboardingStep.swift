//
//  OnboardingStep.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

enum OnboardingStep: Int, CaseIterable {
    case welcome = 0
    case babyProfile = 1
    
    var title: String {
        switch self {
        case .welcome:
            return "Welcome to BabyPulse"
        case .babyProfile:
            return "Create Baby Profile"
        }
    }
    
    var subtitle: String {
        switch self {
        case .welcome:
            return "Track your baby's activities and get personalized insights"
        case .babyProfile:
            return "Tell us about your little one"
        }
    }
}
