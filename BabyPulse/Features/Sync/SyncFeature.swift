import SwiftUI
import SwiftData
import ComposableArchitecture
import Foundation
import Combine

// MARK: - Sync Feature
@Reducer
struct SyncFeature {
    @ObservableState
    struct State: Equatable {
        // Sync state
        var isSyncing = false
        var isOnline = true
        var lastSyncDate: Date?
        var syncEnabled = false
        var autoSyncEnabled = false
        var syncInterval: TimeInterval = 3600 // Default to 1 hour
        
        // Error state
        var errorMessage: String?
        
        // Premium access
        var hasPremiumAccess = false
        
        // Sync status
        var syncStatus = "Ready to sync"
        var syncProgress: Double?
        
        // Data usage metrics
        var profilesCount = 0
        var feedingsCount = 0
        var diapersCount = 0
        var sleepCount = 0
        var healthCount = 0
        var profilesLastSync: Date?
        var feedingsLastSync: Date?
        var diapersLastSync: Date?
        var sleepLastSync: Date?
        var healthLastSync: Date?
        var totalSyncSize = "0 KB"
        
        // Computed properties
        var formattedLastSyncDate: String {
            guard let lastSyncDate = lastSyncDate else {
                return "Never"
            }
            
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            return formatter.string(from: lastSyncDate)
        }
        
        var timeSinceLastSync: String {
            guard let lastSyncDate = lastSyncDate else {
                return "No previous sync"
            }
            
            let formatter = RelativeDateTimeFormatter()
            formatter.unitsStyle = .abbreviated
            return formatter.localizedString(for: lastSyncDate, relativeTo: Date())
        }
        
        var canSync: Bool {
            isOnline && !isSyncing && syncEnabled
        }
        
        var totalDataItems: Int {
            profilesCount + feedingsCount + diapersCount + sleepCount + healthCount
        }
    }
    
    enum Action: Equatable {
        case onAppear
        case loadSyncInfo
        case syncInfoLoaded(SyncInfo)
        case connectivityChanged(Bool)
        case premiumStatusChanged(Bool)
        case autoSyncToggled
        case syncIntervalChanged(TimeInterval)
        case syncNow
        case forceRefresh
        case clearSyncLog
        case syncStarted(SyncOperation)
        case syncProgressUpdated(Double, String)
        case syncCompleted(SyncOperation)
        case syncFailed(SyncOperation, String)
        case dismissError
        case checkConnectivity
        case loadMetrics
        case metricsLoaded(SyncMetrics)
    }
    
    @Dependency(\.syncManager) var syncManager
    @Dependency(\.supabaseService) var supabaseService
    @Dependency(\.revenueCatService) var revenueCatService
    @Dependency(\.userDefaults) var userDefaults
    @Dependency(\.date) var date
    @Dependency(\.continuousClock) var clock
    
    var body: some Reducer<State, Action> {
        Reduce { state, action in
            switch action {
            case .onAppear:
                return .merge(
                    .send(.loadSyncInfo),
                    .send(.checkConnectivity)
                )
                
            case .loadSyncInfo:
                return .run { send in
                    let syncInfo = await loadSyncInfo()
                    await send(.syncInfoLoaded(syncInfo))
                }
                
            case let .syncInfoLoaded(syncInfo):
                state.autoSyncEnabled = syncInfo.autoSyncEnabled
                state.syncInterval = syncInfo.syncInterval
                state.lastSyncDate = syncInfo.lastSyncDate
                state.syncEnabled = syncInfo.syncEnabled
                state.hasPremiumAccess = syncInfo.hasPremiumAccess
                return .send(.loadMetrics)
                
            case let .connectivityChanged(isOnline):
                state.isOnline = isOnline
                if !isOnline && state.isSyncing {
                    state.isSyncing = false
                    state.syncStatus = "Sync interrupted - offline"
                    state.errorMessage = "Network connection lost during sync"
                }
                return .none
                
            case let .premiumStatusChanged(hasPremium):
                state.hasPremiumAccess = hasPremium
                return .none
                
            case .autoSyncToggled:
                state.autoSyncEnabled.toggle()
                return .run { [autoSyncEnabled = state.autoSyncEnabled] send in
                    await saveAutoSyncSetting(autoSyncEnabled)
                }
                
            case let .syncIntervalChanged(interval):
                state.syncInterval = interval
                return .run { send in
                    await saveSyncInterval(interval)
                }
                
            case .syncNow:
                guard state.canSync else {
                    if !state.isOnline {
                        state.errorMessage = "Cannot sync while offline. Please check your internet connection."
                    }
                    return .none
                }
                
                return .send(.syncStarted(.manual))
                
            case .forceRefresh:
                guard state.canSync else {
                    if !state.isOnline {
                        state.errorMessage = "Cannot perform force refresh while offline. Please check your internet connection."
                    }
                    return .none
                }
                
                return .send(.syncStarted(.forceRefresh))
                
            case .clearSyncLog:
                return .send(.syncStarted(.clearLog))
                
            case let .syncStarted(operation):
                state.isSyncing = true
                state.syncProgress = 0.0
                state.errorMessage = nil
                
                switch operation {
                case .manual:
                    state.syncStatus = "Starting sync..."
                case .forceRefresh:
                    state.syncStatus = "Starting force refresh..."
                case .clearLog:
                    state.syncStatus = "Clearing sync logs..."
                }
                
                return .run { [isOnline = state.isOnline] send in
                    do {
                        try await performSyncOperation(operation, isOnline: isOnline) { progress, status in
                            await send(.syncProgressUpdated(progress, status))
                        }
                        await send(.syncCompleted(operation))
                    } catch {
                        await send(.syncFailed(operation, error.localizedDescription))
                    }
                }
                
            case let .syncProgressUpdated(progress, status):
                state.syncProgress = progress
                state.syncStatus = status
                return .none
                
            case let .syncCompleted(operation):
                state.isSyncing = false
                state.syncProgress = nil
                state.lastSyncDate = date.now
                
                switch operation {
                case .manual:
                    state.syncStatus = "Sync completed"
                case .forceRefresh:
                    state.syncStatus = "Force refresh completed"
                case .clearLog:
                    state.syncStatus = "Sync logs cleared"
                }
                
                return .merge(
                    .run { send in
                        await saveLastSyncDate(date.now)
                    },
                    .send(.loadMetrics)
                )
                
            case let .syncFailed(operation, error):
                state.isSyncing = false
                state.syncProgress = nil
                state.errorMessage = "\(operation.displayName) failed: \(error)"
                
                switch operation {
                case .manual:
                    state.syncStatus = "Sync failed"
                case .forceRefresh:
                    state.syncStatus = "Force refresh failed"
                case .clearLog:
                    state.syncStatus = "Failed to clear logs"
                }
                return .none
                
            case .dismissError:
                state.errorMessage = nil
                return .none
                
            case .checkConnectivity:
                return .run { send in
                    // Start periodic connectivity checking
                    for await _ in clock.timer(interval: .seconds(10)) {
                        let isOnline = await checkNetworkConnectivity()
                        await send(.connectivityChanged(isOnline))
                    }
                }
                
            case .loadMetrics:
                return .run { send in
                    let metrics = await loadSyncMetrics()
                    await send(.metricsLoaded(metrics))
                }
                
            case let .metricsLoaded(metrics):
                state.profilesCount = metrics.profilesCount
                state.feedingsCount = metrics.feedingsCount
                state.diapersCount = metrics.diapersCount
                state.sleepCount = metrics.sleepCount
                state.healthCount = metrics.healthCount
                state.profilesLastSync = metrics.profilesLastSync
                state.feedingsLastSync = metrics.feedingsLastSync
                state.diapersLastSync = metrics.diapersLastSync
                state.sleepLastSync = metrics.sleepLastSync
                state.healthLastSync = metrics.healthLastSync
                state.totalSyncSize = metrics.totalSyncSize
                return .none
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func loadSyncInfo() async -> SyncInfo {
        let autoSyncEnabled = userDefaults.bool(forKey: "autoSyncEnabled")
        let syncInterval = userDefaults.double(forKey: "syncInterval")
        let lastSyncTimestamp = userDefaults.double(forKey: "lastSyncDate")
        
        let lastSyncDate = lastSyncTimestamp > 0 ? Date(timeIntervalSince1970: lastSyncTimestamp) : nil
        let finalSyncInterval = syncInterval > 0 ? syncInterval : 3600
        
        return SyncInfo(
            autoSyncEnabled: autoSyncEnabled,
            syncInterval: finalSyncInterval,
            lastSyncDate: lastSyncDate,
            syncEnabled: supabaseService.isSyncEnabled,
            hasPremiumAccess: await revenueCatService.hasPremiumAccess()
        )
    }
    
    private func saveAutoSyncSetting(_ enabled: Bool) async {
        userDefaults.set(enabled, forKey: "autoSyncEnabled")
    }
    
    private func saveSyncInterval(_ interval: TimeInterval) async {
        userDefaults.set(interval, forKey: "syncInterval")
    }
    
    private func saveLastSyncDate(_ date: Date) async {
        userDefaults.set(date.timeIntervalSince1970, forKey: "lastSyncDate")
    }
    
    private func checkNetworkConnectivity() async -> Bool {
        // Simple simulation - 95% chance of being online
        // In a real app, you would use NWPathMonitor
        return Double.random(in: 0...1) < 0.95
    }
    
    private func loadSyncMetrics() async -> SyncMetrics {
        // In a real app, these would be loaded from your database
        let profilesCount = Int.random(in: 1...5)
        let feedingsCount = Int.random(in: 30...150)
        let diapersCount = Int.random(in: 25...120)
        let sleepCount = Int.random(in: 20...100)
        let healthCount = Int.random(in: 5...30)
        
        let totalItems = profilesCount + feedingsCount + diapersCount + sleepCount + healthCount
        let estimatedSizeBytes = totalItems * 2048 // Assume average 2KB per item
        
        let totalSyncSize: String
        if estimatedSizeBytes < 1024 {
            totalSyncSize = "\(estimatedSizeBytes) B"
        } else if estimatedSizeBytes < 1024 * 1024 {
            totalSyncSize = String(format: "%.1f KB", Double(estimatedSizeBytes) / 1024.0)
        } else {
            totalSyncSize = String(format: "%.1f MB", Double(estimatedSizeBytes) / (1024.0 * 1024.0))
        }
        
        return SyncMetrics(
            profilesCount: profilesCount,
            feedingsCount: feedingsCount,
            diapersCount: diapersCount,
            sleepCount: sleepCount,
            healthCount: healthCount,
            profilesLastSync: nil,
            feedingsLastSync: nil,
            diapersLastSync: nil,
            sleepLastSync: nil,
            healthLastSync: nil,
            totalSyncSize: totalSyncSize
        )
    }
    
    private func performSyncOperation(
        _ operation: SyncOperation,
        isOnline: Bool,
        progressCallback: @escaping (Double, String) async -> Void
    ) async throws {
        let steps = operation.steps
        
        for (index, step) in steps.enumerated() {
            // Check if still online
            guard isOnline else {
                throw SyncError.networkLost
            }
            
            let progress = Double(index + 1) / Double(steps.count)
            await progressCallback(progress, step.message)
            
            // Simulate network delay
            let delay = UInt64.random(in: step.minDelay...step.maxDelay)
            try await clock.sleep(for: .nanoseconds(delay))
        }
    }
}

// MARK: - Supporting Types

struct SyncInfo: Equatable {
    let autoSyncEnabled: Bool
    let syncInterval: TimeInterval
    let lastSyncDate: Date?
    let syncEnabled: Bool
    let hasPremiumAccess: Bool
}

struct SyncMetrics: Equatable {
    let profilesCount: Int
    let feedingsCount: Int
    let diapersCount: Int
    let sleepCount: Int
    let healthCount: Int
    let profilesLastSync: Date?
    let feedingsLastSync: Date?
    let diapersLastSync: Date?
    let sleepLastSync: Date?
    let healthLastSync: Date?
    let totalSyncSize: String
}

enum SyncOperation: Equatable {
    case manual
    case forceRefresh
    case clearLog
    
    var displayName: String {
        switch self {
        case .manual: return "Sync"
        case .forceRefresh: return "Force refresh"
        case .clearLog: return "Clear logs"
        }
    }
    
    var steps: [SyncStep] {
        switch self {
        case .manual:
            return [
                SyncStep(message: "Connecting to server...", minDelay: 300_000_000, maxDelay: 800_000_000),
                SyncStep(message: "Authenticating...", minDelay: 300_000_000, maxDelay: 800_000_000),
                SyncStep(message: "Preparing data...", minDelay: 300_000_000, maxDelay: 800_000_000),
                SyncStep(message: "Syncing profiles...", minDelay: 300_000_000, maxDelay: 800_000_000),
                SyncStep(message: "Syncing feedings...", minDelay: 300_000_000, maxDelay: 800_000_000),
                SyncStep(message: "Syncing diapers...", minDelay: 300_000_000, maxDelay: 800_000_000),
                SyncStep(message: "Syncing sleep records...", minDelay: 300_000_000, maxDelay: 800_000_000),
                SyncStep(message: "Syncing health records...", minDelay: 300_000_000, maxDelay: 800_000_000),
                SyncStep(message: "Finalizing sync...", minDelay: 300_000_000, maxDelay: 800_000_000),
                SyncStep(message: "Completing sync...", minDelay: 300_000_000, maxDelay: 800_000_000)
            ]
            
        case .forceRefresh:
            return [
                SyncStep(message: "Connecting to server...", minDelay: 400_000_000, maxDelay: 1_000_000_000),
                SyncStep(message: "Authenticating...", minDelay: 400_000_000, maxDelay: 1_000_000_000),
                SyncStep(message: "Backing up local data...", minDelay: 400_000_000, maxDelay: 1_000_000_000),
                SyncStep(message: "Preparing to reset...", minDelay: 400_000_000, maxDelay: 1_000_000_000),
                SyncStep(message: "Clearing local database...", minDelay: 400_000_000, maxDelay: 1_000_000_000),
                SyncStep(message: "Downloading profiles...", minDelay: 400_000_000, maxDelay: 1_000_000_000),
                SyncStep(message: "Downloading feeding data...", minDelay: 400_000_000, maxDelay: 1_000_000_000),
                SyncStep(message: "Downloading diaper records...", minDelay: 400_000_000, maxDelay: 1_000_000_000),
                SyncStep(message: "Downloading sleep records...", minDelay: 400_000_000, maxDelay: 1_000_000_000),
                SyncStep(message: "Downloading health records...", minDelay: 400_000_000, maxDelay: 1_000_000_000),
                SyncStep(message: "Downloading settings...", minDelay: 400_000_000, maxDelay: 1_000_000_000),
                SyncStep(message: "Processing downloaded data...", minDelay: 400_000_000, maxDelay: 1_000_000_000),
                SyncStep(message: "Rebuilding local database...", minDelay: 400_000_000, maxDelay: 1_000_000_000),
                SyncStep(message: "Verifying data integrity...", minDelay: 400_000_000, maxDelay: 1_000_000_000),
                SyncStep(message: "Finalizing force refresh...", minDelay: 400_000_000, maxDelay: 1_000_000_000)
            ]
            
        case .clearLog:
            return [
                SyncStep(message: "Clearing sync logs...", minDelay: 1_500_000_000, maxDelay: 1_500_000_000)
            ]
        }
    }
}

struct SyncStep: Equatable {
    let message: String
    let minDelay: UInt64
    let maxDelay: UInt64
}

enum SyncError: Error, LocalizedError {
    case networkLost
    case authenticationFailed
    case serverError
    
    var errorDescription: String? {
        switch self {
        case .networkLost:
            return "Network connection lost during sync"
        case .authenticationFailed:
            return "Authentication failed"
        case .serverError:
            return "Server error occurred"
        }
    }
}

// MARK: - Dependencies

extension DependencyValues {
    var syncManager: SyncManager {
        get { self[SyncManagerKey.self] }
        set { self[SyncManagerKey.self] = newValue }
    }
    
    var userDefaults: UserDefaults {
        get { self[UserDefaultsKey.self] }
        set { self[UserDefaultsKey.self] = newValue }
    }
}

private enum SyncManagerKey: DependencyKey {
    static let liveValue = SyncManager.shared
}

private enum UserDefaultsKey: DependencyKey {
    static let liveValue = UserDefaults.standard
} 