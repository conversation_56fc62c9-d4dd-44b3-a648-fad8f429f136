import SwiftUI
import SwiftData
import ComposableArchitecture
import Foundation
import PhotosUI
import UserNotifications

// MARK: - Settings Feature
@Reducer
struct SettingsFeature {
    @ObservableState
    struct State: Equatable {
        // Baby management
        var babies: [Baby] = []
        var selectedBabyID: UUID?
        
        // App settings
        var unitSystem: UnitSystem = .metric
        var darkModeEnabled = false
        
        // Notification settings
        var notificationsEnabled = true
        var reminderTime = Date()
        var feedingNotificationsEnabled = false
        var sleepNotificationsEnabled = false
        var growthNotificationsEnabled = false
        var healthNotificationsEnabled = false
        var insightNotificationsEnabled = false
        var quietHoursEnabled = false
        var quietHoursStart = Date()
        var quietHoursEnd = Date()
        var hasNotificationPermission = false
        
        // Privacy settings
        var analyticsEnabled = true
        var personalizationEnabled = true
        var dataSharingEnabled = false
        var locationEnabled = false
        
        // Sync and backup
        var autoSyncEnabled = false
        var syncEnabled = false
        var hasPremiumAccess = false
        var isExporting = false
        
        // UI state
        var isLoading = false
        var errorMessage: String?
        var showingBabyProfileEdit = false
        var showingAddBabyProfile = false
        
        // Baby editing state
        var editingBaby: Baby?
        var tempBabyName = ""
        var tempBabyBirthDate = Date()
        var tempBabyGender: Gender = .male
        var tempSelectedPhoto: PhotosPickerItem?
        var tempPhotoData: Data?
        var nameError: String?
        
        // Computed properties
        var selectedBaby: Baby? {
            babies.first { $0.id == selectedBabyID }
        }
        
        var hasMultipleBabies: Bool {
            babies.count > 1
        }
        
        var canSaveBaby: Bool {
            !tempBabyName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && nameError == nil
        }
    }
    
    enum Action: Equatable {
        case onAppear
        case loadData
        case dataLoaded(SettingsData)
        case loadingFailed(String)
        
        // Baby management
        case babySelected(UUID)
        case startEditingBaby(Baby)
        case startAddingBaby
        case cancelBabyEdit
        case saveBaby
        case deleteBaby(Baby)
        case tempBabyNameChanged(String)
        case tempBabyBirthDateChanged(Date)
        case tempBabyGenderChanged(Gender)
        case tempPhotoSelected(PhotosPickerItem?)
        case photoDataLoaded(Data?)
        
        // Settings changes
        case unitSystemChanged(UnitSystem)
        case darkModeToggled
        case notificationsToggled
        case reminderTimeChanged(Date)
        case feedingNotificationsToggled
        case sleepNotificationsToggled
        case growthNotificationsToggled
        case healthNotificationsToggled
        case insightNotificationsToggled
        case quietHoursToggled
        case quietHoursStartChanged(Date)
        case quietHoursEndChanged(Date)
        
        // Privacy settings
        case analyticsToggled
        case personalizationToggled
        case dataSharingToggled
        case locationToggled
        
        // Sync and backup
        case autoSyncToggled
        case syncToggled
        case exportData
        case exportCompleted
        case exportFailed(String)
        
        // Permissions
        case requestNotificationPermission
        case notificationPermissionResult(Bool)
    }
    
    @Dependency(\.modelContext) var modelContext
    @Dependency(\.userNotifications) var userNotifications
    
    var body: some Reducer<State, Action> {
        Reduce { state, action in
            switch action {
            case .onAppear:
                return .send(.loadData)
                
            case .loadData:
                state.isLoading = true
                state.errorMessage = nil
                
                return .run { send in
                    do {
                        let settingsData = try await loadSettingsData()
                        await send(.dataLoaded(settingsData))
                    } catch {
                        await send(.loadingFailed(error.localizedDescription))
                    }
                }
                
            case let .dataLoaded(settingsData):
                state.isLoading = false
                state.babies = settingsData.babies
                state.selectedBabyID = settingsData.selectedBabyID
                state.unitSystem = settingsData.unitSystem
                state.darkModeEnabled = settingsData.darkModeEnabled
                state.notificationsEnabled = settingsData.notificationsEnabled
                state.reminderTime = settingsData.reminderTime
                state.feedingNotificationsEnabled = settingsData.feedingNotificationsEnabled
                state.sleepNotificationsEnabled = settingsData.sleepNotificationsEnabled
                state.growthNotificationsEnabled = settingsData.growthNotificationsEnabled
                state.healthNotificationsEnabled = settingsData.healthNotificationsEnabled
                state.insightNotificationsEnabled = settingsData.insightNotificationsEnabled
                state.quietHoursEnabled = settingsData.quietHoursEnabled
                state.quietHoursStart = settingsData.quietHoursStart
                state.quietHoursEnd = settingsData.quietHoursEnd
                state.analyticsEnabled = settingsData.analyticsEnabled
                state.personalizationEnabled = settingsData.personalizationEnabled
                state.dataSharingEnabled = settingsData.dataSharingEnabled
                state.locationEnabled = settingsData.locationEnabled
                state.autoSyncEnabled = settingsData.autoSyncEnabled
                state.syncEnabled = settingsData.syncEnabled
                state.hasPremiumAccess = settingsData.hasPremiumAccess
                state.hasNotificationPermission = settingsData.hasNotificationPermission
                return .none
                
            case let .loadingFailed(error):
                state.isLoading = false
                state.errorMessage = error
                return .none
                
            // Baby management actions
            case let .babySelected(id):
                state.selectedBabyID = id
                return .run { send in
                    try await updateSelectedBaby(id)
                }
                
            case let .startEditingBaby(baby):
                state.editingBaby = baby
                state.tempBabyName = baby.name
                state.tempBabyBirthDate = baby.birthDate
                state.tempBabyGender = baby.gender
                state.tempPhotoData = baby.photoData
                state.tempSelectedPhoto = nil
                state.nameError = nil
                state.showingBabyProfileEdit = true
                return .none
                
            case .startAddingBaby:
                state.editingBaby = nil
                state.tempBabyName = ""
                state.tempBabyBirthDate = Date()
                state.tempBabyGender = .male
                state.tempPhotoData = nil
                state.tempSelectedPhoto = nil
                state.nameError = nil
                state.showingAddBabyProfile = true
                return .none
                
            case .cancelBabyEdit:
                state.showingBabyProfileEdit = false
                state.showingAddBabyProfile = false
                state.editingBaby = nil
                state.nameError = nil
                return .none
                
            case .saveBaby:
                guard state.canSaveBaby else { return .none }
                
                return .run { [state] send in
                    do {
                        if let editingBaby = state.editingBaby {
                            // Update existing baby
                            try await updateBaby(
                                editingBaby,
                                name: state.tempBabyName,
                                birthDate: state.tempBabyBirthDate,
                                gender: state.tempBabyGender,
                                photoData: state.tempPhotoData
                            )
                        } else {
                            // Create new baby
                            try await createBaby(
                                name: state.tempBabyName,
                                birthDate: state.tempBabyBirthDate,
                                gender: state.tempBabyGender,
                                photoData: state.tempPhotoData
                            )
                        }
                        await send(.loadData)
                        await send(.cancelBabyEdit)
                    } catch {
                        await send(.loadingFailed(error.localizedDescription))
                    }
                }
                
            case let .deleteBaby(baby):
                return .run { send in
                    do {
                        try await deleteBaby(baby)
                        await send(.loadData)
                    } catch {
                        await send(.loadingFailed(error.localizedDescription))
                    }
                }
                
            case let .tempBabyNameChanged(name):
                state.tempBabyName = name
                state.nameError = validateBabyName(name)
                return .none
                
            case let .tempBabyBirthDateChanged(date):
                state.tempBabyBirthDate = date
                return .none
                
            case let .tempBabyGenderChanged(gender):
                state.tempBabyGender = gender
                return .none
                
            case let .tempPhotoSelected(item):
                state.tempSelectedPhoto = item
                return .run { send in
                    if let item = item {
                        let data = try await loadPhotoData(from: item)
                        await send(.photoDataLoaded(data))
                    } else {
                        await send(.photoDataLoaded(nil))
                    }
                }
                
            case let .photoDataLoaded(data):
                state.tempPhotoData = data
                return .none
                
            // Settings changes
            case let .unitSystemChanged(unitSystem):
                state.unitSystem = unitSystem
                return .run { send in
                    try await updateUnitSystem(unitSystem)
                }
                
            case .darkModeToggled:
                state.darkModeEnabled.toggle()
                return .run { [darkModeEnabled = state.darkModeEnabled] send in
                    try await updateDarkMode(darkModeEnabled)
                }
                
            case .notificationsToggled:
                state.notificationsEnabled.toggle()
                return .run { [enabled = state.notificationsEnabled] send in
                    try await updateNotifications(enabled)
                }
                
            case let .reminderTimeChanged(time):
                state.reminderTime = time
                return .run { send in
                    try await updateReminderTime(time)
                }
                
            case .feedingNotificationsToggled:
                state.feedingNotificationsEnabled.toggle()
                return .run { [enabled = state.feedingNotificationsEnabled] send in
                    try await updateFeedingNotifications(enabled)
                }
                
            case .sleepNotificationsToggled:
                state.sleepNotificationsEnabled.toggle()
                return .run { [enabled = state.sleepNotificationsEnabled] send in
                    try await updateSleepNotifications(enabled)
                }
                
            case .growthNotificationsToggled:
                state.growthNotificationsEnabled.toggle()
                return .run { [enabled = state.growthNotificationsEnabled] send in
                    try await updateGrowthNotifications(enabled)
                }
                
            case .healthNotificationsToggled:
                state.healthNotificationsEnabled.toggle()
                return .run { [enabled = state.healthNotificationsEnabled] send in
                    try await updateHealthNotifications(enabled)
                }
                
            case .insightNotificationsToggled:
                state.insightNotificationsEnabled.toggle()
                return .run { [enabled = state.insightNotificationsEnabled] send in
                    try await updateInsightNotifications(enabled)
                }
                
            case .quietHoursToggled:
                state.quietHoursEnabled.toggle()
                return .run { [enabled = state.quietHoursEnabled] send in
                    try await updateQuietHours(enabled)
                }
                
            case let .quietHoursStartChanged(time):
                state.quietHoursStart = time
                return .run { send in
                    try await updateQuietHoursStart(time)
                }
                
            case let .quietHoursEndChanged(time):
                state.quietHoursEnd = time
                return .run { send in
                    try await updateQuietHoursEnd(time)
                }
                
            // Privacy settings
            case .analyticsToggled:
                state.analyticsEnabled.toggle()
                return .run { [enabled = state.analyticsEnabled] send in
                    try await updateAnalytics(enabled)
                }
                
            case .personalizationToggled:
                state.personalizationEnabled.toggle()
                return .run { [enabled = state.personalizationEnabled] send in
                    try await updatePersonalization(enabled)
                }
                
            case .dataSharingToggled:
                state.dataSharingEnabled.toggle()
                return .run { [enabled = state.dataSharingEnabled] send in
                    try await updateDataSharing(enabled)
                }
                
            case .locationToggled:
                state.locationEnabled.toggle()
                return .run { [enabled = state.locationEnabled] send in
                    try await updateLocation(enabled)
                }
                
            // Sync and backup
            case .autoSyncToggled:
                state.autoSyncEnabled.toggle()
                return .run { [enabled = state.autoSyncEnabled] send in
                    try await updateAutoSync(enabled)
                }
                
            case .syncToggled:
                state.syncEnabled.toggle()
                return .run { [enabled = state.syncEnabled] send in
                    try await updateSync(enabled)
                }
                
            case .exportData:
                state.isExporting = true
                return .run { send in
                    do {
                        try await exportUserData()
                        await send(.exportCompleted)
                    } catch {
                        await send(.exportFailed(error.localizedDescription))
                    }
                }
                
            case .exportCompleted:
                state.isExporting = false
                return .none
                
            case let .exportFailed(error):
                state.isExporting = false
                state.errorMessage = error
                return .none
                
            // Permissions
            case .requestNotificationPermission:
                return .run { send in
                    let granted = try await userNotifications.requestAuthorization([.alert, .badge, .sound])
                    await send(.notificationPermissionResult(granted))
                }
                
            case let .notificationPermissionResult(granted):
                state.hasNotificationPermission = granted
                return .none
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func loadSettingsData() async throws -> SettingsData {
        // Load babies
        let babyDescriptor = FetchDescriptor<Baby>(sortBy: [SortDescriptor(\.name)])
        let babies = try modelContext.fetch(babyDescriptor)
        
        // Load user preferences
        let preferencesDescriptor = FetchDescriptor<UserPreferences>()
        let preferences = try modelContext.fetch(preferencesDescriptor)
        let userPreferences = preferences.first ?? UserPreferences()
        
        // Check notification permission
        let notificationSettings = await userNotifications.notificationSettings()
        let hasNotificationPermission = notificationSettings.authorizationStatus == .authorized
        
        return SettingsData(
            babies: babies,
            selectedBabyID: userPreferences.selectedBabyID,
            unitSystem: userPreferences.unitSystem,
            darkModeEnabled: userPreferences.darkModeEnabled,
            notificationsEnabled: userPreferences.notificationsEnabled,
            reminderTime: userPreferences.reminderTime ?? Date(),
            feedingNotificationsEnabled: userPreferences.feedingNotificationsEnabled,
            sleepNotificationsEnabled: userPreferences.sleepNotificationsEnabled,
            growthNotificationsEnabled: userPreferences.growthNotificationsEnabled,
            healthNotificationsEnabled: userPreferences.healthNotificationsEnabled,
            insightNotificationsEnabled: userPreferences.insightNotificationsEnabled,
            quietHoursEnabled: userPreferences.quietHoursEnabled,
            quietHoursStart: userPreferences.quietHoursStart ?? Date(),
            quietHoursEnd: userPreferences.quietHoursEnd ?? Date(),
            analyticsEnabled: userPreferences.analyticsEnabled,
            personalizationEnabled: userPreferences.personalizationEnabled,
            dataSharingEnabled: userPreferences.dataSharingEnabled,
            locationEnabled: userPreferences.locationEnabled,
            autoSyncEnabled: userPreferences.autoSyncEnabled,
            syncEnabled: userPreferences.syncEnabled,
            hasPremiumAccess: false, // TODO: Implement premium check
            hasNotificationPermission: hasNotificationPermission
        )
    }
    
    private func validateBabyName(_ name: String) -> String? {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        if trimmedName.isEmpty {
            return "Name is required"
        }
        if trimmedName.count < 2 {
            return "Name must be at least 2 characters"
        }
        return nil
    }
    
    private func loadPhotoData(from item: PhotosPickerItem) async throws -> Data? {
        return try await item.loadTransferable(type: Data.self)
    }
    
    // Additional helper methods would be implemented here for database operations
    private func updateSelectedBaby(_ id: UUID) async throws {
        modelContext.updateSelectedBabyID(id)
    }
    
    private func updateBaby(_ baby: Baby, name: String, birthDate: Date, gender: Gender, photoData: Data?) async throws {
        baby.name = name
        baby.birthDate = birthDate
        baby.gender = gender
        baby.photoData = photoData
        baby.updatedAt = Date()
        try modelContext.save()
    }
    
    private func createBaby(name: String, birthDate: Date, gender: Gender, photoData: Data?) async throws {
        let baby = Baby(name: name, birthDate: birthDate, gender: gender)
        baby.photoData = photoData
        modelContext.insert(baby)
        try modelContext.save()
    }
    
    private func deleteBaby(_ baby: Baby) async throws {
        modelContext.delete(baby)
        try modelContext.save()
    }
    
    // Settings update methods
    private func updateUnitSystem(_ unitSystem: UnitSystem) async throws {
        UserPreferencesManager.shared.unitSystem = unitSystem
    }
    
    private func updateDarkMode(_ enabled: Bool) async throws {
        // Update user preferences
    }
    
    private func updateNotifications(_ enabled: Bool) async throws {
        // Update notification settings
    }
    
    private func updateReminderTime(_ time: Date) async throws {
        // Update reminder time
    }
    
    private func updateFeedingNotifications(_ enabled: Bool) async throws {
        // Update feeding notifications
    }
    
    private func updateSleepNotifications(_ enabled: Bool) async throws {
        // Update sleep notifications
    }
    
    private func updateGrowthNotifications(_ enabled: Bool) async throws {
        // Update growth notifications
    }
    
    private func updateHealthNotifications(_ enabled: Bool) async throws {
        // Update health notifications
    }
    
    private func updateInsightNotifications(_ enabled: Bool) async throws {
        // Update insight notifications
    }
    
    private func updateQuietHours(_ enabled: Bool) async throws {
        // Update quiet hours
    }
    
    private func updateQuietHoursStart(_ time: Date) async throws {
        // Update quiet hours start
    }
    
    private func updateQuietHoursEnd(_ time: Date) async throws {
        // Update quiet hours end
    }
    
    private func updateAnalytics(_ enabled: Bool) async throws {
        // Update analytics setting
    }
    
    private func updatePersonalization(_ enabled: Bool) async throws {
        // Update personalization setting
    }
    
    private func updateDataSharing(_ enabled: Bool) async throws {
        // Update data sharing setting
    }
    
    private func updateLocation(_ enabled: Bool) async throws {
        // Update location setting
    }
    
    private func updateAutoSync(_ enabled: Bool) async throws {
        // Update auto sync setting
    }
    
    private func updateSync(_ enabled: Bool) async throws {
        // Update sync setting
    }
    
    private func exportUserData() async throws {
        // Implement data export functionality
    }
}

// MARK: - Supporting Types

struct SettingsData: Equatable {
    let babies: [Baby]
    let selectedBabyID: UUID?
    let unitSystem: UnitSystem
    let darkModeEnabled: Bool
    let notificationsEnabled: Bool
    let reminderTime: Date
    let feedingNotificationsEnabled: Bool
    let sleepNotificationsEnabled: Bool
    let growthNotificationsEnabled: Bool
    let healthNotificationsEnabled: Bool
    let insightNotificationsEnabled: Bool
    let quietHoursEnabled: Bool
    let quietHoursStart: Date
    let quietHoursEnd: Date
    let analyticsEnabled: Bool
    let personalizationEnabled: Bool
    let dataSharingEnabled: Bool
    let locationEnabled: Bool
    let autoSyncEnabled: Bool
    let syncEnabled: Bool
    let hasPremiumAccess: Bool
    let hasNotificationPermission: Bool
}

// MARK: - Dependencies

extension DependencyValues {
    var userNotifications: UNUserNotificationCenter {
        get { self[UserNotificationsKey.self] }
        set { self[UserNotificationsKey.self] = newValue }
    }
}

private enum UserNotificationsKey: DependencyKey {
    static let liveValue = UNUserNotificationCenter.current()
} 