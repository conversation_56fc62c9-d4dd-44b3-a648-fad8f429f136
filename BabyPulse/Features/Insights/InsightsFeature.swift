import SwiftUI
import SwiftData
import ComposableArchitecture
import Foundation

// MARK: - Insights Feature
@Reducer
struct InsightsFeature {
    @ObservableState
    struct State: Equatable {
        var insights: [Insight] = []
        var filteredInsights: [Insight] = []
        var selectedCategory: Insight.InsightCategory?
        var timeRange: TimeRange = .daily
        var sortOption: SortOption = .newest
        var isLoading = false
        var errorMessage: String?
        var selectedInsight: Insight?
        var showingInsightDetail = false
        var showingFilterSheet = false
        var currentBaby: Baby?
        
        // Computed properties
        var hasInsights: Bool {
            !filteredInsights.isEmpty
        }
        
        var categoryCounts: [Insight.InsightCategory: Int] {
            Dictionary(grouping: insights) { $0.category }
                .mapValues { $0.count }
        }
        
        var needsAttentionCount: Int {
            insights.filter { $0.needsAttention }.count
        }
    }
    
    enum Action: Equatable {
        case onAppear
        case loadData
        case dataLoaded([Insight])
        case loadingFailed(String)
        case categorySelected(Insight.InsightCategory?)
        case timeRangeChanged(TimeRange)
        case sortOptionChanged(SortOption)
        case insightSelected(Insight)
        case dismissInsightDetail
        case showFilterSheet
        case hideFilterSheet
        case refreshInsights
        case applyFiltersAndSort
    }
    
    @Dependency(\.modelContext) var modelContext
    @Dependency(\.date) var date
    @Dependency(\.calendar) var calendar
    
    var body: some Reducer<State, Action> {
        Reduce { state, action in
            switch action {
            case .onAppear:
                return .send(.loadData)
                
            case .loadData:
                state.isLoading = true
                state.errorMessage = nil
                
                return .run { send in
                    do {
                        let insights = try await loadInsights()
                        await send(.dataLoaded(insights))
                    } catch {
                        await send(.loadingFailed(error.localizedDescription))
                    }
                }
                
            case let .dataLoaded(insights):
                state.isLoading = false
                state.insights = insights
                return .send(.applyFiltersAndSort)
                
            case let .loadingFailed(error):
                state.isLoading = false
                state.errorMessage = error
                return .none
                
            case let .categorySelected(category):
                state.selectedCategory = state.selectedCategory == category ? nil : category
                return .send(.applyFiltersAndSort)
                
            case let .timeRangeChanged(timeRange):
                state.timeRange = timeRange
                return .send(.applyFiltersAndSort)
                
            case let .sortOptionChanged(sortOption):
                state.sortOption = sortOption
                return .send(.applyFiltersAndSort)
                
            case let .insightSelected(insight):
                state.selectedInsight = insight
                state.showingInsightDetail = true
                return .none
                
            case .dismissInsightDetail:
                state.selectedInsight = nil
                state.showingInsightDetail = false
                return .none
                
            case .showFilterSheet:
                state.showingFilterSheet = true
                return .none
                
            case .hideFilterSheet:
                state.showingFilterSheet = false
                return .none
                
            case .refreshInsights:
                return .run { send in
                    // Trigger manual insight generation
                    await send(.loadData)
                }
                
            case .applyFiltersAndSort:
                state.filteredInsights = applyFiltersAndSort(
                    insights: state.insights,
                    selectedCategory: state.selectedCategory,
                    timeRange: state.timeRange,
                    sortOption: state.sortOption,
                    currentDate: date.now,
                    calendar: calendar
                )
                return .none
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func loadInsights() async throws -> [Insight] {
        guard let baby = modelContext.fetchSelectedBaby() else {
            return []
        }
        
        let descriptor = FetchDescriptor<Insight>(
            sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
        )
        let allInsights = try modelContext.fetch(descriptor)
        
        // Filter for current baby
        return allInsights.filter { $0.baby?.id == baby.id }
    }
    
    private func applyFiltersAndSort(
        insights: [Insight],
        selectedCategory: Insight.InsightCategory?,
        timeRange: TimeRange,
        sortOption: SortOption,
        currentDate: Date,
        calendar: Calendar
    ) -> [Insight] {
        var filtered = insights
        
        // Filter by category
        if let selectedCategory = selectedCategory {
            filtered = filtered.filter { $0.category == selectedCategory }
        }
        
        // Filter out duplicates
        filtered = filterDuplicateInsights(filtered)
        
        // Filter by time range
        filtered = filterByTimeRange(filtered, timeRange: timeRange, currentDate: currentDate, calendar: calendar)
        
        // Sort insights
        return sortInsights(filtered, sortOption: sortOption)
    }
    
    private func filterDuplicateInsights(_ insights: [Insight]) -> [Insight] {
        let groupedInsights = Dictionary(grouping: insights) { $0.hashKey }
        
        return groupedInsights.compactMap { (_, insightsWithSameHash) -> Insight? in
            if insightsWithSameHash.first?.hashKey.isEmpty ?? true {
                return insightsWithSameHash.first
            }
            return insightsWithSameHash.sorted { $0.timestamp > $1.timestamp }.first
        }
    }
    
    private func filterByTimeRange(
        _ insights: [Insight],
        timeRange: TimeRange,
        currentDate: Date,
        calendar: Calendar
    ) -> [Insight] {
        switch timeRange {
        case .daily:
            return insights.filter { calendar.isDateInToday($0.timestamp) }
            
        case .weekly:
            let components = calendar.dateComponents([.weekOfYear, .yearForWeekOfYear], from: currentDate)
            return insights.filter { insight in
                let insightComponents = calendar.dateComponents([.weekOfYear, .yearForWeekOfYear], from: insight.timestamp)
                return insightComponents.weekOfYear == components.weekOfYear &&
                       insightComponents.yearForWeekOfYear == components.yearForWeekOfYear
            }
            
        case .monthly:
            let components = calendar.dateComponents([.month, .year], from: currentDate)
            return insights.filter { insight in
                let insightComponents = calendar.dateComponents([.month, .year], from: insight.timestamp)
                return insightComponents.month == components.month &&
                       insightComponents.year == components.year
            }
        }
    }
    
    private func sortInsights(_ insights: [Insight], sortOption: SortOption) -> [Insight] {
        switch sortOption {
        case .newest:
            return insights.sorted { $0.timestamp > $1.timestamp }
        case .oldest:
            return insights.sorted { $0.timestamp < $1.timestamp }
        case .needsAttention:
            return insights.sorted { ($0.needsAttention ? 1 : 0) > ($1.needsAttention ? 1 : 0) }
        case .highestConfidence:
            return insights.sorted { $0.confidence > $1.confidence }
        case .severity:
            let severityValue: [Insight.InsightSeverity: Int] = [
                .urgent: 3,
                .warning: 2,
                .info: 1
            ]
            return insights.sorted { insight1, insight2 in
                (severityValue[insight1.severity] ?? 0) > (severityValue[insight2.severity] ?? 0)
            }
        }
    }
}

// MARK: - Supporting Enums

extension InsightsFeature {
    enum TimeRange: String, CaseIterable, Identifiable {
        case daily = "Daily"
        case weekly = "Weekly"
        case monthly = "Monthly"
        
        var id: String { self.rawValue }
        
        var displayName: String {
            switch self {
            case .daily: return "Today"
            case .weekly: return "This Week"
            case .monthly: return "This Month"
            }
        }
    }
    
    enum SortOption: String, CaseIterable, Identifiable {
        case newest = "Newest"
        case oldest = "Oldest"
        case needsAttention = "Needs Attention"
        case highestConfidence = "Highest Confidence"
        case severity = "Severity"
        
        var id: String { self.rawValue }
        
        var displayName: String {
            switch self {
            case .newest: return "Newest First"
            case .oldest: return "Oldest First"
            case .needsAttention: return "Needs Attention"
            case .highestConfidence: return "Highest Confidence"
            case .severity: return "By Severity"
            }
        }
        
        var icon: String {
            switch self {
            case .newest: return "arrow.down"
            case .oldest: return "arrow.up"
            case .needsAttention: return "exclamationmark.triangle"
            case .highestConfidence: return "checkmark.circle"
            case .severity: return "exclamationmark.octagon"
            }
        }
    }
} 