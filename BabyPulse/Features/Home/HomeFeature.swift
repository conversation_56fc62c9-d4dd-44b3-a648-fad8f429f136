import SwiftUI
import SwiftData
import ComposableArchitecture
import Foundation

// MARK: - Home Feature
@Reducer
struct HomeFeature {
    @ObservableState
    struct State: Equatable {
        var currentBaby: Baby?
        var dailySummary = DailySummary()
        var riskStatus: RiskStatus = .normal
        var riskMessage = "Everything looks good!"
        var insights: [Insight] = []
        var isLoading = false
        var errorMessage: String?
        var selectedInsight: Insight?
        var showingInsightDetail = false
        
        // Computed properties
        var hasCurrentBaby: Bool {
            currentBaby != nil
        }
        
        var summaryCards: [SummaryCard] {
            [
                SummaryCard(
                    title: "Feedings",
                    value: "\(dailySummary.totalFeedings)",
                    subtitle: "today",
                    icon: "bottle.fill",
                    color: .blue
                ),
                SummaryCard(
                    title: "Diapers",
                    value: "\(dailySummary.totalDiapers)",
                    subtitle: "changes",
                    icon: "circle.fill",
                    color: .green
                ),
                SummaryCard(
                    title: "Sleep",
                    value: String(format: "%.1fh", dailySummary.totalSleepHours),
                    subtitle: "total",
                    icon: "moon.fill",
                    color: .purple
                )
            ]
        }
    }
    
    enum Action: Equatable {
        case onAppear
        case loadData
        case dataLoaded(Baby?, DailySummary, [Insight])
        case loadingFailed(String)
        case riskAssessed(RiskStatus, String)
        case insightSelected(Insight)
        case dismissInsightDetail
        case refreshData
    }
    
    @Dependency(\.modelContext) var modelContext
    @Dependency(\.date) var date
    @Dependency(\.calendar) var calendar
    
    var body: some Reducer<State, Action> {
        Reduce { state, action in
            switch action {
            case .onAppear:
                return .send(.loadData)
                
            case .loadData:
                state.isLoading = true
                state.errorMessage = nil
                
                return .run { send in
                    do {
                        let baby = try await loadCurrentBaby()
                        let summary = try await loadDailySummary(for: baby)
                        let insights = try await loadRecentInsights(for: baby)
                        
                        await send(.dataLoaded(baby, summary, insights))
                        
                        if let baby = baby {
                            let (riskStatus, riskMessage) = assessRisk(summary: summary, baby: baby)
                            await send(.riskAssessed(riskStatus, riskMessage))
                        }
                    } catch {
                        await send(.loadingFailed(error.localizedDescription))
                    }
                }
                
            case let .dataLoaded(baby, summary, insights):
                state.isLoading = false
                state.currentBaby = baby
                state.dailySummary = summary
                state.insights = insights
                return .none
                
            case let .loadingFailed(error):
                state.isLoading = false
                state.errorMessage = error
                return .none
                
            case let .riskAssessed(status, message):
                state.riskStatus = status
                state.riskMessage = message
                return .none
                
            case let .insightSelected(insight):
                state.selectedInsight = insight
                state.showingInsightDetail = true
                return .none
                
            case .dismissInsightDetail:
                state.selectedInsight = nil
                state.showingInsightDetail = false
                return .none
                
            case .refreshData:
                return .send(.loadData)
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func loadCurrentBaby() async throws -> Baby? {
        guard let baby = modelContext.fetchSelectedBaby() else {
            // Try to get the first available baby
            let descriptor = FetchDescriptor<Baby>()
            let babies = try modelContext.fetch(descriptor)
            
            if let firstBaby = babies.first {
                modelContext.updateSelectedBabyID(firstBaby.id)
                return firstBaby
            }
            return nil
        }
        return baby
    }
    
    private func loadDailySummary(for baby: Baby?) async throws -> DailySummary {
        guard let baby = baby else { return DailySummary() }
        
        let today = calendar.startOfDay(for: date.now)
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: today)!
        
        // Fetch feeding entries
        let feedingDescriptor = FetchDescriptor<FeedingEntry>(
            predicate: #Predicate { entry in
                entry.timestamp >= today && entry.timestamp < tomorrow
            }
        )
        let feedings = try modelContext.fetch(feedingDescriptor)
        
        // Fetch diaper entries
        let diaperDescriptor = FetchDescriptor<DiaperEntry>(
            predicate: #Predicate { entry in
                entry.timestamp >= today && entry.timestamp < tomorrow
            }
        )
        let diapers = try modelContext.fetch(diaperDescriptor)
        
        // Fetch sleep entries
        let sleepDescriptor = FetchDescriptor<SleepEntry>(
            predicate: #Predicate { entry in
                entry.timestamp >= today && entry.timestamp < tomorrow
            }
        )
        let sleeps = try modelContext.fetch(sleepDescriptor)
        
        return calculateDailySummary(feedings: feedings, diapers: diapers, sleeps: sleeps)
    }
    
    private func loadRecentInsights(for baby: Baby?) async throws -> [Insight] {
        guard let baby = baby else { return [] }
        
        let descriptor = FetchDescriptor<Insight>(
            sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
        )
        let allInsights = try modelContext.fetch(descriptor)
        
        // Filter for current baby and take recent ones
        return allInsights
            .filter { $0.baby?.id == baby.id }
            .prefix(3)
            .map { $0 }
    }
    
    private func calculateDailySummary(
        feedings: [FeedingEntry],
        diapers: [DiaperEntry],
        sleeps: [SleepEntry]
    ) -> DailySummary {
        var summary = DailySummary()
        
        // Calculate feeding metrics
        summary.totalFeedings = feedings.count
        summary.breastFeedings = feedings.filter { $0.type == .breastfeeding }.count
        summary.bottleFeedings = feedings.filter { $0.type == .bottleFeeding }.count
        summary.solidFeedings = feedings.filter { $0.type == .solidFood }.count
        summary.totalFeedingVolume = feedings
            .filter { $0.type == .bottleFeeding }
            .compactMap { $0.volume }
            .reduce(0, +)
        
        // Calculate diaper metrics
        summary.totalDiapers = diapers.count
        summary.wetDiapers = diapers.filter { $0.type == .wet }.count
        summary.dirtyDiapers = diapers.filter { $0.type == .dirty }.count
        summary.mixedDiapers = diapers.filter { $0.type == .mixed }.count
        
        // Calculate sleep metrics
        var totalSleepMinutes = 0
        for sleep in sleeps {
            if let endTime = sleep.endTime {
                let duration = Int(endTime.timeIntervalSince(sleep.timestamp) / 60)
                totalSleepMinutes += duration
            } else {
                // For ongoing sleep, calculate duration until now
                let duration = Int(date.now.timeIntervalSince(sleep.timestamp) / 60)
                totalSleepMinutes += duration
            }
        }
        
        summary.totalSleepHours = Double(totalSleepMinutes) / 60.0
        summary.sleepSessions = sleeps.count
        
        return summary
    }
    
    private func assessRisk(summary: DailySummary, baby: Baby) -> (RiskStatus, String) {
        let ageInDays = baby.ageInDays
        
        if summary.totalDiapers < 4 && ageInDays < 30 {
            return (.warning, "Low diaper count today. Newborns typically have 6-8 wet diapers per day.")
        } else if summary.totalFeedings < 5 && ageInDays < 90 {
            return (.warning, "Low feeding count today. Young babies typically feed 8-12 times per day.")
        } else if summary.totalSleepHours < 10 && ageInDays < 180 {
            return (.warning, "Low sleep hours today. Babies typically need 14-17 hours of sleep per day.")
        } else {
            return (.normal, "Everything looks good! Your baby's patterns are within normal ranges.")
        }
    }
}

// MARK: - Supporting Models

struct DailySummary: Equatable {
    // Feeding metrics
    var totalFeedings: Int = 0
    var breastFeedings: Int = 0
    var bottleFeedings: Int = 0
    var solidFeedings: Int = 0
    var totalFeedingVolume: Double = 0.0
    
    // Diaper metrics
    var totalDiapers: Int = 0
    var wetDiapers: Int = 0
    var dirtyDiapers: Int = 0
    var mixedDiapers: Int = 0
    
    // Sleep metrics
    var totalSleepHours: Double = 0.0
    var sleepSessions: Int = 0
}

enum RiskStatus: Equatable {
    case normal
    case warning
    case alert
    
    var color: Color {
        switch self {
        case .normal: return .green
        case .warning: return .orange
        case .alert: return .red
        }
    }
    
    var icon: String {
        switch self {
        case .normal: return "checkmark.circle.fill"
        case .warning: return "exclamationmark.triangle.fill"
        case .alert: return "exclamationmark.octagon.fill"
        }
    }
}

struct SummaryCard: Equatable, Identifiable {
    let id = UUID()
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let color: Color
} 