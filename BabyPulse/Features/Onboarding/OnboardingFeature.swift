import SwiftUI
import SwiftData
import ComposableArchitecture
import Foundation
import PhotosUI

// MARK: - Onboarding Feature
@Reducer
struct OnboardingFeature {
    @ObservableState
    struct State: Equatable {
        var currentStep: OnboardingStep = .welcome
        var babyName = ""
        var birthDate = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
        var gender: Gender = .male
        var selectedPhoto: PhotosPickerItem?
        var photoData: Data?
        var unitSystem: UnitSystem = .metric
        var notificationsEnabled = true
        var reminderTime = Calendar.current.date(bySettingHour: 20, minute: 0, second: 0, of: Date()) ?? Date()
        var darkModeEnabled = false
        var selectedAvatarIndex: Int?

        // Validation
        var nameError: String?

        // Loading states
        var isLoading = false
        var isCompletingOnboarding = false
        var errorMessage: String?

        // Computed properties
        var canMoveToNextStep: Bool {
            switch currentStep {
            case .welcome:
                return true
            case .babyProfile:
                return isValidBabyProfile
            }
        }

        var isValidBabyProfile: Bool {
            !babyName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && nameError == nil
        }

        var babyAge: String {
            let calendar = Calendar.current
            let ageComponents = calendar.dateComponents([.year, .month, .day], from: birthDate, to: Date())

            if let years = ageComponents.year, let months = ageComponents.month, let days = ageComponents.day {
                if years > 0 {
                    return "\(years) year\(years == 1 ? "" : "s"), \(months) month\(months == 1 ? "" : "s")"
                } else if months > 0 {
                    return "\(months) month\(months == 1 ? "" : "s"), \(days) day\(days == 1 ? "" : "s")"
                } else {
                    return "\(days) day\(days == 1 ? "" : "s")"
                }
            }
            return "0 days"
        }

        var avatarOptions: [String] {
            ["baby.avatar.1", "baby.avatar.2", "baby.avatar.3", "baby.avatar.4"]
        }
    }

    enum Action: Equatable {
        case moveToNextStep
        case moveToPreviousStep
        case babyNameChanged(String)
        case birthDateChanged(Date)
        case genderChanged(Gender)
        case photoSelected(PhotosPickerItem?)
        case photoDataLoaded(Data?)
        case avatarSelected(Int)
        case unitSystemChanged(UnitSystem)
        case notificationsToggled
        case reminderTimeChanged(Date)
        case darkModeToggled
        case completeOnboarding
        case onboardingCompleted
        case onboardingFailed(String)
        case validateBabyProfile
    }

    @Dependency(\.modelContext) var modelContext

    var body: some Reducer<State, Action> {
        Reduce { state, action in
            switch action {
            case .moveToNextStep:
                guard state.canMoveToNextStep else { return .none }

                if let nextStep = OnboardingStep(rawValue: state.currentStep.rawValue + 1) {
                    state.currentStep = nextStep
                    return .none
                } else {
                    // Complete onboarding if at last step
                    return .send(.completeOnboarding)
                }

            case .moveToPreviousStep:
                if let previousStep = OnboardingStep(rawValue: state.currentStep.rawValue - 1) {
                    state.currentStep = previousStep
                }
                return .none

            case let .babyNameChanged(name):
                state.babyName = name
                return .send(.validateBabyProfile)

            case let .birthDateChanged(date):
                state.birthDate = date
                return .none

            case let .genderChanged(gender):
                state.gender = gender
                return .none

            case let .photoSelected(item):
                state.selectedPhoto = item
                state.selectedAvatarIndex = nil

                return .run { send in
                    if let item = item {
                        let data = try await loadPhotoData(from: item)
                        await send(.photoDataLoaded(data))
                    } else {
                        await send(.photoDataLoaded(nil))
                    }
                }

            case let .photoDataLoaded(data):
                state.photoData = data
                return .none

            case let .avatarSelected(index):
                state.selectedAvatarIndex = index
                state.photoData = nil
                state.selectedPhoto = nil
                return .none

            case let .unitSystemChanged(unitSystem):
                state.unitSystem = unitSystem
                return .none

            case .notificationsToggled:
                state.notificationsEnabled.toggle()
                return .none

            case let .reminderTimeChanged(time):
                state.reminderTime = time
                return .none

            case .darkModeToggled:
                state.darkModeEnabled.toggle()
                return .none

            case .completeOnboarding:
                state.isCompletingOnboarding = true
                state.errorMessage = nil

                return .run { [state] send in
                    do {
                        try await completeOnboarding(with: state)
                        await send(.onboardingCompleted)
                    } catch {
                        await send(.onboardingFailed(error.localizedDescription))
                    }
                }

            case .onboardingCompleted:
                state.isCompletingOnboarding = false
                return .none

            case let .onboardingFailed(error):
                state.isCompletingOnboarding = false
                state.errorMessage = error
                return .none

            case .validateBabyProfile:
                state.nameError = validateBabyName(state.babyName)
                return .none
            }
        }
    }

    // MARK: - Private Methods

    private func loadPhotoData(from item: PhotosPickerItem) async throws -> Data? {
        return try await item.loadTransferable(type: Data.self)
    }

    private func validateBabyName(_ name: String) -> String? {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        if trimmedName.isEmpty {
            return "Please enter your baby's name"
        }
        return nil
    }

    private func completeOnboarding(with state: State) async throws {
        // Create baby profile
        let baby = createBabyProfile(from: state)
        modelContext.insert(baby)

        // Create user preferences
        let preferences = createUserPreferences(from: state, babyID: baby.id)
        modelContext.insert(preferences)

        // Save changes
        try modelContext.save()
    }

    private func createBabyProfile(from state: State) -> Baby {
        var finalPhotoData = state.photoData

        // If avatar is selected, use that instead of photo data
        if let avatarIndex = state.selectedAvatarIndex {
            if let avatarImage = UIImage(named: state.avatarOptions[avatarIndex]) {
                finalPhotoData = avatarImage.jpegData(compressionQuality: 0.8)
            }
        }

        return Baby(
            name: state.babyName,
            birthDate: state.birthDate,
            gender: state.gender,
            photoData: finalPhotoData
        )
    }

    private func createUserPreferences(from state: State, babyID: UUID) -> UserPreferences {
        let preferences = UserPreferences()
        preferences.selectedBabyID = babyID
        preferences.unitSystem = state.unitSystem
        preferences.notificationsEnabled = state.notificationsEnabled
        preferences.reminderTime = state.notificationsEnabled ? state.reminderTime : nil
        preferences.darkModeEnabled = state.darkModeEnabled
        preferences.onboardingCompleted = true
        return preferences
    }
}

// MARK: - Supporting Types
// OnboardingStep moved to BabyPulse/Models/OnboardingStep.swift to avoid duplication