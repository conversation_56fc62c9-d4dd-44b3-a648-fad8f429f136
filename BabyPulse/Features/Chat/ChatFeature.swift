import SwiftUI
import SwiftData
import ComposableArchitecture
import Foundation

// MARK: - Chat Feature
@Reducer
struct ChatFeature {
    @ObservableState
    struct State: Equatable {
        var messages: [ChatMessage] = []
        var inputText = ""
        var isLoading = false
        var isStreaming = false
        var errorMessage: String?
        var suggestedQuestions: [String] = []
        var thread: ChatThread?
        var currentBaby: Baby?
        
        // Computed properties
        var canSendMessage: Bool {
            !inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && !isLoading && !isStreaming
        }
        
        var hasMessages: Bool {
            !messages.isEmpty
        }
        
        var lastMessage: ChatMessage? {
            messages.last
        }
    }
    
    enum Action: Equatable {
        case onAppear
        case loadMessages
        case messagesLoaded([ChatMessage])
        case loadingFailed(String)
        case inputTextChanged(String)
        case sendMessage
        case messageSent(ChatMessage)
        case responseReceived(String)
        case streamingStarted
        case streamingEnded
        case streamingTokenReceived(String)
        case suggestedQuestionTapped(String)
        case generateSuggestedQuestions
        case suggestedQuestionsGenerated([String])
        case createNewThread
        case threadCreated(ChatThread)
    }
    
    @Dependency(\.modelContext) var modelContext
    @Dependency(\.llmService) var llmService
    @Dependency(\.supabaseService) var supabaseService
    
    var body: some Reducer<State, Action> {
        Reduce { state, action in
            switch action {
            case .onAppear:
                return .merge(
                    .send(.loadMessages),
                    .send(.generateSuggestedQuestions)
                )
                
            case .loadMessages:
                return .run { [thread = state.thread] send in
                    do {
                        if let thread = thread {
                            let messages = try await loadMessagesForThread(thread)
                            await send(.messagesLoaded(messages))
                        } else {
                            // Create new thread if none exists
                            await send(.createNewThread)
                        }
                    } catch {
                        await send(.loadingFailed(error.localizedDescription))
                    }
                }
                
            case let .messagesLoaded(messages):
                state.messages = messages
                return .send(.generateSuggestedQuestions)
                
            case let .loadingFailed(error):
                state.errorMessage = error
                return .none
                
            case let .inputTextChanged(text):
                state.inputText = text
                return .none
                
            case .sendMessage:
                let trimmedText = state.inputText.trimmingCharacters(in: .whitespacesAndNewlines)
                guard !trimmedText.isEmpty else { return .none }
                
                // Clear input and set loading state
                state.inputText = ""
                state.isLoading = true
                state.errorMessage = nil
                
                return .run { [thread = state.thread, currentBaby = state.currentBaby] send in
                    do {
                        // Create user message
                        guard let thread = thread else {
                            await send(.loadingFailed("No active thread"))
                            return
                        }
                        
                        let userMessage = ChatMessage.createUserMessage(content: trimmedText, thread: thread)
                        try await saveMessage(userMessage)
                        await send(.messageSent(userMessage))
                        
                        // Try Supabase first, then fall back to local LLM
                        do {
                            let response = try await supabaseService.sendChatMessage(
                                babyId: currentBaby?.id.uuidString ?? "",
                                message: trimmedText,
                                threadId: thread.id.uuidString
                            )
                            
                            let assistantMessage = ChatMessage.createAssistantMessage(
                                content: response.message,
                                tokenCount: response.message.count / 4,
                                thread: thread
                            )
                            try await saveMessage(assistantMessage)
                            await send(.responseReceived(response.message))
                            
                        } catch {
                            // Fall back to local LLM with streaming
                            await send(.streamingStarted)
                            
                            let babyData = getBabyDataContext(for: currentBaby)
                            let stream = llmService.generateStreamingResponse(
                                userMessage: trimmedText,
                                babyData: babyData,
                                timeoutSeconds: 45
                            )
                            
                            var fullResponse = ""
                            for try await token in stream {
                                fullResponse += token
                                await send(.streamingTokenReceived(token))
                            }
                            
                            let assistantMessage = ChatMessage.createAssistantMessage(
                                content: fullResponse,
                                tokenCount: fullResponse.count / 4,
                                thread: thread
                            )
                            try await saveMessage(assistantMessage)
                            await send(.streamingEnded)
                        }
                        
                    } catch {
                        await send(.loadingFailed(error.localizedDescription))
                    }
                }
                
            case let .messageSent(message):
                state.messages.append(message)
                return .none
                
            case let .responseReceived(response):
                state.isLoading = false
                if let thread = state.thread {
                    let assistantMessage = ChatMessage.createAssistantMessage(
                        content: response,
                        tokenCount: response.count / 4,
                        thread: thread
                    )
                    state.messages.append(assistantMessage)
                }
                return .send(.generateSuggestedQuestions)
                
            case .streamingStarted:
                state.isLoading = false
                state.isStreaming = true
                if let thread = state.thread {
                    let streamingMessage = ChatMessage.createStreamingAssistantMessage(thread: thread)
                    state.messages.append(streamingMessage)
                }
                return .none
                
            case .streamingEnded:
                state.isStreaming = false
                return .send(.generateSuggestedQuestions)
                
            case let .streamingTokenReceived(token):
                // Update the last message (streaming message) with new token
                if var lastMessage = state.messages.last, lastMessage.isStreaming {
                    lastMessage.content += token
                    state.messages[state.messages.count - 1] = lastMessage
                }
                return .none
                
            case let .suggestedQuestionTapped(question):
                state.inputText = question
                return .none
                
            case .generateSuggestedQuestions:
                return .run { [currentBaby = state.currentBaby, messages = state.messages] send in
                    let questions = await generateSuggestedQuestions(for: currentBaby, messages: messages)
                    await send(.suggestedQuestionsGenerated(questions))
                }
                
            case let .suggestedQuestionsGenerated(questions):
                state.suggestedQuestions = questions
                return .none
                
            case .createNewThread:
                return .run { send in
                    do {
                        let thread = try await createNewChatThread()
                        await send(.threadCreated(thread))
                    } catch {
                        await send(.loadingFailed(error.localizedDescription))
                    }
                }
                
            case let .threadCreated(thread):
                state.thread = thread
                return .send(.loadMessages)
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func loadMessagesForThread(_ thread: ChatThread) async throws -> [ChatMessage] {
        let descriptor = FetchDescriptor<ChatMessage>(
            sortBy: [SortDescriptor(\.timestamp, order: .forward)]
        )
        let allMessages = try modelContext.fetch(descriptor)
        
        return allMessages.filter { $0.thread?.id == thread.id }
    }
    
    private func saveMessage(_ message: ChatMessage) async throws {
        modelContext.insert(message)
        try modelContext.save()
    }
    
    private func createNewChatThread() async throws -> ChatThread {
        let descriptor = FetchDescriptor<Baby>(sortBy: [SortDescriptor(\.name)])
        let babies = try modelContext.fetch(descriptor)
        
        guard let firstBaby = babies.first else {
            throw ChatError.noBabyFound
        }
        
        let newThread = ChatThread(baby: firstBaby)
        modelContext.insert(newThread)
        
        let welcomeMessage = ChatMessage.createAssistantMessage(
            content: "Hello! I'm your BabyPulse assistant. I can help answer questions about \(firstBaby.name)'s patterns and development. How can I help you today?",
            thread: newThread
        )
        modelContext.insert(welcomeMessage)
        
        try modelContext.save()
        return newThread
    }
    
    private func getBabyDataContext(for baby: Baby?) -> String {
        guard let baby = baby else { return "No baby data available." }
        
        // Generate context about the baby's recent activities
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: today)!
        
        // This would typically fetch recent entries and summarize them
        return """
        Baby: \(baby.name)
        Age: \(baby.ageInDays) days old
        Recent activity summary would go here...
        """
    }
    
    private func generateSuggestedQuestions(for baby: Baby?, messages: [ChatMessage]) async -> [String] {
        guard let baby = baby else {
            return [
                "How can I track my baby's feeding schedule?",
                "What should I know about sleep patterns?",
                "How do I log diaper changes?"
            ]
        }
        
        // Generate context-aware suggestions based on baby's age and recent messages
        let ageInDays = baby.ageInDays
        
        if ageInDays < 30 {
            return [
                "How often should \(baby.name) be feeding?",
                "What's normal for newborn sleep patterns?",
                "How many diaper changes are typical per day?",
                "When should I be concerned about weight gain?"
            ]
        } else if ageInDays < 180 {
            return [
                "When can \(baby.name) start solid foods?",
                "How can I establish a sleep routine?",
                "What developmental milestones should I watch for?",
                "How do I know if \(baby.name) is getting enough nutrition?"
            ]
        } else {
            return [
                "What foods are safe for \(baby.name) to try?",
                "How can I encourage independent play?",
                "What are typical sleep patterns at this age?",
                "How do I handle separation anxiety?"
            ]
        }
    }
}

// MARK: - Supporting Types

enum ChatError: Error, LocalizedError {
    case noBabyFound
    case threadNotFound
    case messageSaveFailed
    
    var errorDescription: String? {
        switch self {
        case .noBabyFound:
            return "No baby profile found"
        case .threadNotFound:
            return "Chat thread not found"
        case .messageSaveFailed:
            return "Failed to save message"
        }
    }
}

// MARK: - Dependencies

extension DependencyValues {
    var llmService: LLMService {
        get { self[LLMServiceKey.self] }
        set { self[LLMServiceKey.self] = newValue }
    }
    
    var supabaseService: SupabaseService {
        get { self[SupabaseServiceKey.self] }
        set { self[SupabaseServiceKey.self] = newValue }
    }
}

private enum LLMServiceKey: DependencyKey {
    static let liveValue = LLMService()
}

private enum SupabaseServiceKey: DependencyKey {
    static let liveValue = SupabaseService.shared
} 