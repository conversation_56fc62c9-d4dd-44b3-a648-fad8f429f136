import SwiftUI
import SwiftData
import ComposableArchitecture
import Foundation

// MARK: - Main Log Entry Feature
@Reducer
struct LogEntryFeature {
    @ObservableState
    struct State: Equatable {
        var selectedCategory: LogCategory?
        var timestamp = Date()
        var notes = ""
        var isEditMode = false
        var currentBaby: Baby?

        // Sub-features
        var feedingState = FeedingEntryFeature.State()
        var diaperState = DiaperEntryFeature.State()
        var sleepState = SleepEntryFeature.State()
        var growthState = GrowthEntryFeature.State()
        var healthState = HealthEntryFeature.State()
        var generalActivityState = GeneralActivityEntryFeature.State()

        // Validation
        var hasValidationErrors: Bool {
            switch selectedCategory {
            case .feeding:
                return feedingState.hasValidationErrors
            case .diaper:
                return diaperState.hasValidationErrors
            case .sleep:
                return sleepState.hasValidationErrors
            case .growth:
                return growthState.hasValidationErrors
            case .health:
                return healthState.hasValidationErrors
            default:
                return generalActivityState.hasValidationErrors
            }
        }
    }

    enum Action: Equatable {
        case categorySelected(LogCategory)
        case timestampChanged(Date)
        case notesChanged(String)
        case saveEntry
        case cancelEntry
        case entryLoaded

        // Sub-feature actions
        case feeding(FeedingEntryFeature.Action)
        case diaper(DiaperEntryFeature.Action)
        case sleep(SleepEntryFeature.Action)
        case growth(GrowthEntryFeature.Action)
        case health(HealthEntryFeature.Action)
        case generalActivity(GeneralActivityEntryFeature.Action)
    }

    @Dependency(\.modelContext) var modelContext
    @Dependency(\.dismiss) var dismiss

    var body: some Reducer<State, Action> {
        Scope(state: \.feedingState, action: \.feeding) {
            FeedingEntryFeature()
        }

        Scope(state: \.diaperState, action: \.diaper) {
            DiaperEntryFeature()
        }

        Scope(state: \.sleepState, action: \.sleep) {
            SleepEntryFeature()
        }

        Scope(state: \.growthState, action: \.growth) {
            GrowthEntryFeature()
        }

        Scope(state: \.healthState, action: \.health) {
            HealthEntryFeature()
        }

        Scope(state: \.generalActivityState, action: \.generalActivity) {
            GeneralActivityEntryFeature()
        }

        Reduce { state, action in
            switch action {
            case let .categorySelected(category):
                state.selectedCategory = category
                return .none

            case let .timestampChanged(timestamp):
                state.timestamp = timestamp
                return .none

            case let .notesChanged(notes):
                state.notes = notes
                return .none

            case .saveEntry:
                guard !state.hasValidationErrors else { return .none }

                return .run { [state] send in
                    try await saveEntryToDatabase(state)
                    await dismiss()
                }

            case .cancelEntry:
                return .run { _ in
                    await dismiss()
                }

            case .entryLoaded:
                // Handle loading existing entry for editing
                return .none

            case .feeding, .diaper, .sleep, .growth, .health, .generalActivity:
                return .none
            }
        }
    }

    private func saveEntryToDatabase(_ state: State) async throws {
        guard let baby = state.currentBaby else { return }

        switch state.selectedCategory {
        case .feeding:
            let entry = createFeedingEntry(from: state)
            modelContext.insert(entry)

        case .diaper:
            let entry = createDiaperEntry(from: state)
            modelContext.insert(entry)

        case .sleep:
            let entry = createSleepEntry(from: state)
            modelContext.insert(entry)

        case .growth:
            let entry = createGrowthEntry(from: state)
            modelContext.insert(entry)

        case .health:
            let entry = createHealthEntry(from: state)
            modelContext.insert(entry)

        default:
            let entry = createGeneralActivityEntry(from: state)
            modelContext.insert(entry)
        }

        try modelContext.save()
    }

    // Helper methods to create entries
    private func createFeedingEntry(from state: State) -> FeedingEntry {
        let entry = FeedingEntry(
            timestamp: state.timestamp,
            type: state.feedingState.feedingType,
            notes: state.notes.isEmpty ? nil : state.notes,
            baby: state.currentBaby!
        )

        switch state.feedingState.feedingType {
        case .breastfeeding:
            entry.duration = state.feedingState.duration
            entry.leftBreast = state.feedingState.leftBreast
            entry.rightBreast = state.feedingState.rightBreast

        case .bottleFeeding:
            entry.volume = state.feedingState.volume
            entry.content = state.feedingState.bottleContent

        case .solidFood:
            entry.foodItem = state.feedingState.foodItem.isEmpty ? nil : state.feedingState.foodItem
            entry.foodAmount = state.feedingState.foodAmount.isEmpty ? nil : state.feedingState.foodAmount
            entry.reaction = state.feedingState.reaction.isEmpty ? nil : state.feedingState.reaction
        }

        return entry
    }

    private func createDiaperEntry(from state: State) -> DiaperEntry {
        let entry = DiaperEntry(
            timestamp: state.timestamp,
            type: state.diaperState.diaperType,
            notes: state.notes.isEmpty ? nil : state.notes,
            baby: state.currentBaby!
        )

        if state.diaperState.diaperType == .dirty || state.diaperState.diaperType == .mixed {
            entry.poopColor = state.diaperState.poopColor
            entry.poopConsistency = state.diaperState.poopConsistency
        }

        return entry
    }

    private func createSleepEntry(from state: State) -> SleepEntry {
        let entry = SleepEntry(
            startTime: state.sleepState.startTime,
            endTime: state.sleepState.isOngoing ? nil : state.sleepState.endTime,
            location: state.sleepState.location,
            notes: state.notes.isEmpty ? nil : state.notes,
            baby: state.currentBaby!
        )

        return entry
    }

    private func createGrowthEntry(from state: State) -> GrowthEntry {
        let entry = GrowthEntry(
            timestamp: state.timestamp,
            weight: state.growthState.weightValue,
            height: state.growthState.heightValue,
            headCircumference: state.growthState.headCircumferenceValue,
            notes: state.notes.isEmpty ? nil : state.notes,
            baby: state.currentBaby!
        )

        return entry
    }

    private func createHealthEntry(from state: State) -> HealthEntry {
        let entry = HealthEntry(
            timestamp: state.timestamp,
            type: state.healthState.entryType,
            notes: state.notes.isEmpty ? nil : state.notes,
            baby: state.currentBaby!
        )

        switch state.healthState.entryType {
        case .temperature:
            entry.temperature = state.healthState.temperatureValue
            entry.temperatureUnit = state.healthState.temperatureUnit

        case .medication:
            entry.medicationName = state.healthState.medicationName.isEmpty ? nil : state.healthState.medicationName
            entry.medicationDosage = state.healthState.medicationDosage.isEmpty ? nil : state.healthState.medicationDosage

        case .symptom:
            entry.setSymptoms(state.healthState.selectedSymptoms)

        case .vaccination:
            entry.vaccineName = state.healthState.vaccineName.isEmpty ? nil : state.healthState.vaccineName

        case .appointment:
            entry.appointmentReason = state.healthState.appointmentReason.isEmpty ? nil : state.healthState.appointmentReason
            entry.appointmentProvider = state.healthState.appointmentProvider.isEmpty ? nil : state.healthState.appointmentProvider
        }

        return entry
    }

    private func createGeneralActivityEntry(from state: State) -> GeneralActivityEntry {
        let entry = GeneralActivityEntry(
            timestamp: state.timestamp,
            notes: state.notes.isEmpty ? nil : state.notes,
            baby: state.currentBaby!,
            activityType: state.generalActivityState.activityType,
            customActivityName: state.generalActivityState.activityType == .other ? state.generalActivityState.customName : nil,
            durationMinutes: state.generalActivityState.durationMinutes > 0 ? state.generalActivityState.durationMinutes : nil
        )

        return entry
    }
}

// MARK: - Feeding Entry Feature
@Reducer
struct FeedingEntryFeature {
    @ObservableState
    struct State: Equatable {
        var feedingType: FeedingEntry.FeedingType = .breastfeeding
        var duration = 15
        var volume: Double?
        var volumeText = ""
        var bottleContent: FeedingEntry.BottleContent = .formula
        var leftBreast = false
        var rightBreast = false
        var foodItem = ""
        var foodAmount = ""
        var reaction = ""

        // Validation
        var volumeError: String?
        var foodItemError: String?

        var hasValidationErrors: Bool {
            volumeError != nil || foodItemError != nil
        }
    }

    enum Action: Equatable {
        case feedingTypeChanged(FeedingEntry.FeedingType)
        case durationChanged(Int)
        case volumeChanged(String)
        case bottleContentChanged(FeedingEntry.BottleContent)
        case leftBreastToggled
        case rightBreastToggled
        case foodItemChanged(String)
        case foodAmountChanged(String)
        case reactionChanged(String)
        case validateFields
    }

    var body: some Reducer<State, Action> {
        Reduce { state, action in
            switch action {
            case let .feedingTypeChanged(type):
                state.feedingType = type
                return .send(.validateFields)

            case let .durationChanged(duration):
                state.duration = duration
                return .none

            case let .volumeChanged(text):
                state.volumeText = text
                state.volume = Double(text)
                return .send(.validateFields)

            case let .bottleContentChanged(content):
                state.bottleContent = content
                return .none

            case .leftBreastToggled:
                state.leftBreast.toggle()
                return .none

            case .rightBreastToggled:
                state.rightBreast.toggle()
                return .none

            case let .foodItemChanged(item):
                state.foodItem = item
                return .send(.validateFields)

            case let .foodAmountChanged(amount):
                state.foodAmount = amount
                return .none

            case let .reactionChanged(reaction):
                state.reaction = reaction
                return .none

            case .validateFields:
                return validateFeedingFields(state: &state)
            }
        }
    }

    private func validateFeedingFields(state: inout State) -> Effect<Action> {
        state.volumeError = nil
        state.foodItemError = nil

        switch state.feedingType {
        case .bottleFeeding:
            if state.volumeText.isEmpty {
                state.volumeError = "Volume is required for bottle feeding"
            } else if state.volume == nil || state.volume! <= 0 {
                state.volumeError = "Please enter a valid volume"
            }

        case .solidFood:
            if state.foodItem.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                state.foodItemError = "Food item is required"
            }

        case .breastfeeding:
            break // No specific validation needed
        }

        return .none
    }
}

// MARK: - Diaper Entry Feature
@Reducer
struct DiaperEntryFeature {
    @ObservableState
    struct State: Equatable {
        var diaperType: DiaperEntry.DiaperType = .wet
        var poopColor: DiaperEntry.PoopColor = .yellow
        var poopConsistency: DiaperEntry.PoopConsistency = .seedy

        var hasValidationErrors: Bool {
            false // No validation needed for diaper entries
        }
    }

    enum Action: Equatable {
        case diaperTypeChanged(DiaperEntry.DiaperType)
        case poopColorChanged(DiaperEntry.PoopColor)
        case poopConsistencyChanged(DiaperEntry.PoopConsistency)
    }

    var body: some Reducer<State, Action> {
        Reduce { state, action in
            switch action {
            case let .diaperTypeChanged(type):
                state.diaperType = type
                return .none

            case let .poopColorChanged(color):
                state.poopColor = color
                return .none

            case let .poopConsistencyChanged(consistency):
                state.poopConsistency = consistency
                return .none
            }
        }
    }
}

// MARK: - Sleep Entry Feature
@Reducer
struct SleepEntryFeature {
    @ObservableState
    struct State: Equatable {
        var startTime = Date()
        var endTime = Date().addingTimeInterval(3600)
        var isOngoing = false
        var location: SleepEntry.SleepLocation = .crib

        var hasValidationErrors: Bool {
            !isOngoing && endTime <= startTime
        }
    }

    enum Action: Equatable {
        case startTimeChanged(Date)
        case endTimeChanged(Date)
        case ongoingToggled
        case locationChanged(SleepEntry.SleepLocation)
    }

    var body: some Reducer<State, Action> {
        Reduce { state, action in
            switch action {
            case let .startTimeChanged(time):
                state.startTime = time
                return .none

            case let .endTimeChanged(time):
                state.endTime = time
                return .none

            case .ongoingToggled:
                state.isOngoing.toggle()
                return .none

            case let .locationChanged(location):
                state.location = location
                return .none
            }
        }
    }
}

// MARK: - Growth Entry Feature
@Reducer
struct GrowthEntryFeature {
    @ObservableState
    struct State: Equatable {
        var weightText = ""
        var heightText = ""
        var headCircumferenceText = ""

        // Validation
        var weightError: String?
        var heightError: String?
        var headCircumferenceError: String?

        var hasValidationErrors: Bool {
            weightError != nil || heightError != nil || headCircumferenceError != nil
        }

        var weightValue: Double? {
            Double(weightText)
        }

        var heightValue: Double? {
            Double(heightText)
        }

        var headCircumferenceValue: Double? {
            Double(headCircumferenceText)
        }
    }

    enum Action: Equatable {
        case weightChanged(String)
        case heightChanged(String)
        case headCircumferenceChanged(String)
        case validateFields
    }

    var body: some Reducer<State, Action> {
        Reduce { state, action in
            switch action {
            case let .weightChanged(text):
                state.weightText = text
                return .send(.validateFields)

            case let .heightChanged(text):
                state.heightText = text
                return .send(.validateFields)

            case let .headCircumferenceChanged(text):
                state.headCircumferenceText = text
                return .send(.validateFields)

            case .validateFields:
                return validateGrowthFields(state: &state)
            }
        }
    }

    private func validateGrowthFields(state: inout State) -> Effect<Action> {
        state.weightError = nil
        state.heightError = nil
        state.headCircumferenceError = nil

        if !state.weightText.isEmpty && state.weightValue == nil {
            state.weightError = "Please enter a valid weight"
        }

        if !state.heightText.isEmpty && state.heightValue == nil {
            state.heightError = "Please enter a valid height"
        }

        if !state.headCircumferenceText.isEmpty && state.headCircumferenceValue == nil {
            state.headCircumferenceError = "Please enter a valid head circumference"
        }

        return .none
    }
}

// MARK: - Health Entry Feature
@Reducer
struct HealthEntryFeature {
    @ObservableState
    struct State: Equatable {
        var entryType: HealthEntry.HealthEntryType = .temperature
        var temperatureText = ""
        var temperatureUnit: HealthEntry.TemperatureUnit = .celsius
        var medicationName = ""
        var medicationDosage = ""
        var selectedSymptoms: [HealthEntry.Symptom] = []
        var vaccineName = ""
        var appointmentReason = ""
        var appointmentProvider = ""

        // Validation
        var temperatureError: String?
        var medicationNameError: String?
        var vaccineNameError: String?
        var appointmentReasonError: String?

        var hasValidationErrors: Bool {
            temperatureError != nil || medicationNameError != nil ||
            vaccineNameError != nil || appointmentReasonError != nil
        }

        var temperatureValue: Double? {
            Double(temperatureText)
        }
    }

    enum Action: Equatable {
        case entryTypeChanged(HealthEntry.HealthEntryType)
        case temperatureChanged(String)
        case temperatureUnitChanged(HealthEntry.TemperatureUnit)
        case medicationNameChanged(String)
        case medicationDosageChanged(String)
        case symptomToggled(HealthEntry.Symptom)
        case vaccineNameChanged(String)
        case appointmentReasonChanged(String)
        case appointmentProviderChanged(String)
        case validateFields
    }

    var body: some Reducer<State, Action> {
        Reduce { state, action in
            switch action {
            case let .entryTypeChanged(type):
                state.entryType = type
                return .send(.validateFields)

            case let .temperatureChanged(text):
                state.temperatureText = text
                return .send(.validateFields)

            case let .temperatureUnitChanged(unit):
                state.temperatureUnit = unit
                return .none

            case let .medicationNameChanged(name):
                state.medicationName = name
                return .send(.validateFields)

            case let .medicationDosageChanged(dosage):
                state.medicationDosage = dosage
                return .none

            case let .symptomToggled(symptom):
                if state.selectedSymptoms.contains(symptom) {
                    state.selectedSymptoms.removeAll { $0 == symptom }
                } else {
                    state.selectedSymptoms.append(symptom)
                }
                return .none

            case let .vaccineNameChanged(name):
                state.vaccineName = name
                return .send(.validateFields)

            case let .appointmentReasonChanged(reason):
                state.appointmentReason = reason
                return .send(.validateFields)

            case let .appointmentProviderChanged(provider):
                state.appointmentProvider = provider
                return .none

            case .validateFields:
                return validateHealthFields(state: &state)
            }
        }
    }

    private func validateHealthFields(state: inout State) -> Effect<Action> {
        state.temperatureError = nil
        state.medicationNameError = nil
        state.vaccineNameError = nil
        state.appointmentReasonError = nil

        switch state.entryType {
        case .temperature:
            if state.temperatureText.isEmpty {
                state.temperatureError = "Temperature is required"
            } else if state.temperatureValue == nil {
                state.temperatureError = "Please enter a valid temperature"
            }

        case .medication:
            if state.medicationName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                state.medicationNameError = "Medication name is required"
            }

        case .vaccination:
            if state.vaccineName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                state.vaccineNameError = "Vaccine name is required"
            }

        case .appointment:
            if state.appointmentReason.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                state.appointmentReasonError = "Appointment reason is required"
            }

        case .symptom:
            break // No validation needed
        }

        return .none
    }
}

// MARK: - General Activity Entry Feature
@Reducer
struct GeneralActivityEntryFeature {
    @ObservableState
    struct State: Equatable {
        var activityType: GeneralActivityType = .bath
        var customName = ""
        var durationMinutes = 0

        // Validation
        var customNameError: String?

        var hasValidationErrors: Bool {
            customNameError != nil
        }
    }

    enum Action: Equatable {
        case activityTypeChanged(GeneralActivityType)
        case customNameChanged(String)
        case durationChanged(Int)
        case validateFields
    }

    var body: some Reducer<State, Action> {
        Reduce { state, action in
            switch action {
            case let .activityTypeChanged(type):
                state.activityType = type
                return .send(.validateFields)

            case let .customNameChanged(name):
                state.customName = name
                return .send(.validateFields)

            case let .durationChanged(duration):
                state.durationMinutes = duration
                return .none

            case .validateFields:
                return validateGeneralActivityFields(state: &state)
            }
        }
    }

    private func validateGeneralActivityFields(state: inout State) -> Effect<Action> {
        state.customNameError = nil

        if state.activityType == .other && state.customName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            state.customNameError = "Custom activity name is required"
        }

        return .none
    }
}