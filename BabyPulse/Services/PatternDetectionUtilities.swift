//
//  PatternDetectionUtilities.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation

/// Utilities for detecting patterns in baby data
class PatternDetectionUtilities {
    // MARK: - Cyclical Pattern Detection

    /// Detect cyclical patterns in time series data
    /// - Parameters:
    ///   - timestamps: Array of timestamps
    ///   - values: Array of corresponding values
    ///   - category: The category being analyzed
    ///   - metric: The metric being analyzed
    /// - Returns: Cyclical pattern if detected, nil otherwise
    static func detectCyclicalPattern(
        timestamps: [Date],
        values: [Double],
        category: Insight.InsightCategory,
        metric: String
    ) -> CyclicalPattern? {
        guard timestamps.count == values.count, timestamps.count >= 10 else {
            return nil
        }

        // Sort data points by timestamp
        let sortedData = zip(timestamps, values).sorted { $0.0 < $1.0 }
        let sortedTimestamps = sortedData.map { $0.0 }
        let sortedValues = sortedData.map { $0.1 }

        // Calculate time intervals between data points in hours
        var timeIntervals: [Double] = []
        for i in 0..<sortedTimestamps.count - 1 {
            let interval = sortedTimestamps[i + 1].timeIntervalSince(sortedTimestamps[i]) / 3600
            timeIntervals.append(interval)
        }

        // Calculate average interval
        let averageInterval = timeIntervals.reduce(0, +) / Double(timeIntervals.count)

        // Perform autocorrelation analysis to detect cycles
        let (periodHours, confidence, amplitude, phaseHours) = performAutocorrelationAnalysis(
            timestamps: sortedTimestamps,
            values: sortedValues,
            averageInterval: averageInterval
        )

        // Only return if we have a reasonable confidence
        guard confidence >= 50 else {
            return nil
        }

        // Create description based on the period
        let description = createCyclicalPatternDescription(
            periodHours: periodHours,
            category: category,
            metric: metric
        )

        return CyclicalPattern(
            category: category,
            metric: metric,
            periodHours: periodHours,
            confidence: confidence,
            description: description,
            amplitude: amplitude,
            phaseHours: phaseHours
        )
    }

    /// Perform autocorrelation analysis to detect cycles
    /// - Parameters:
    ///   - timestamps: Sorted timestamps
    ///   - values: Corresponding sorted values
    ///   - averageInterval: Average interval between data points
    /// - Returns: Tuple of (period, confidence, amplitude, phase)
    private static func performAutocorrelationAnalysis(
        timestamps: [Date],
        values: [Double],
        averageInterval: Double
    ) -> (periodHours: Double, confidence: Double, amplitude: Double, phaseHours: Double) {
        // This is a simplified implementation of autocorrelation analysis
        // In a real implementation, this would use more sophisticated algorithms

        // For now, we'll use a simple approach to detect common cycles

        // Calculate mean of values
        let mean = values.reduce(0, +) / Double(values.count)

        // Normalize values
        let normalizedValues = values.map { $0 - mean }

        // Calculate variance
        let variance = normalizedValues.map { $0 * $0 }.reduce(0, +) / Double(normalizedValues.count)
        let stdDev = sqrt(variance)

        // Calculate time range in hours
        let totalTimeRange = timestamps.last!.timeIntervalSince(timestamps.first!) / 3600

        // Try common periods
        let commonPeriods: [Double] = [3, 4, 6, 8, 12, 24, 48]
        var bestPeriod = 24.0 // Default to 24 hours
        var bestCorrelation = 0.0
        var bestAmplitude = 0.0
        var bestPhase = 0.0

        for period in commonPeriods {
            // Skip periods that are too long for our data
            if period > totalTimeRange / 2 {
                continue
            }

            // Calculate correlation for this period
            var sumCorrelation = 0.0
            var sumAmplitude = 0.0
            var sumPhase = 0.0
            var count = 0

            for i in 0..<values.count {
                let timestamp = timestamps[i]
                let hourOfDay = Double(Calendar.current.component(.hour, from: timestamp)) +
                                Double(Calendar.current.component(.minute, from: timestamp)) / 60.0

                // Calculate phase for this data point
                let phase = hourOfDay.truncatingRemainder(dividingBy: period)

                // Find other points with similar phase
                var similarPhaseValues: [Double] = []

                for j in 0..<values.count where i != j {
                    let otherTimestamp = timestamps[j]
                    let otherHourOfDay = Double(Calendar.current.component(.hour, from: otherTimestamp)) +
                                        Double(Calendar.current.component(.minute, from: otherTimestamp)) / 60.0
                    let otherPhase = otherHourOfDay.truncatingRemainder(dividingBy: period)

                    // If phases are similar (within 10% of period)
                    if abs(phase - otherPhase) < period * 0.1 || abs(phase - otherPhase) > period * 0.9 {
                        similarPhaseValues.append(normalizedValues[j])
                    }
                }

                if !similarPhaseValues.isEmpty {
                    // Calculate correlation between this point and similar phase points
                    let avgSimilarValue = similarPhaseValues.reduce(0, +) / Double(similarPhaseValues.count)
                    let correlation = normalizedValues[i] * avgSimilarValue / variance

                    sumCorrelation += correlation
                    sumAmplitude += abs(normalizedValues[i])
                    sumPhase += phase
                    count += 1
                }
            }

            if count > 0 {
                let avgCorrelation = sumCorrelation / Double(count)
                let avgAmplitude = sumAmplitude / Double(count)
                let avgPhase = sumPhase / Double(count)

                if avgCorrelation > bestCorrelation {
                    bestCorrelation = avgCorrelation
                    bestPeriod = period
                    bestAmplitude = avgAmplitude
                    bestPhase = avgPhase
                }
            }
        }

        // Calculate confidence based on correlation
        let confidence = min(100, max(0, bestCorrelation * 100))

        // Calculate amplitude in original units
        let amplitude = bestAmplitude * stdDev

        return (bestPeriod, confidence, amplitude, bestPhase)
    }

    /// Create a description for a cyclical pattern
    /// - Parameters:
    ///   - periodHours: The period in hours
    ///   - category: The category
    ///   - metric: The metric
    /// - Returns: Human-readable description
    private static func createCyclicalPatternDescription(
        periodHours: Double,
        category: Insight.InsightCategory,
        metric: String
    ) -> String {
        let periodText = formatPeriod(periodHours)

        switch (category, metric) {
        case (.sleep, "total_duration"):
            return "Sleep duration follows a \(periodText) cycle"
        case (.sleep, "longest_stretch"):
            return "Longest sleep stretch follows a \(periodText) cycle"
        case (.feeding, "volume"):
            return "Feeding volume follows a \(periodText) cycle"
        case (.feeding, "frequency"):
            return "Feeding frequency follows a \(periodText) cycle"
        case (.diaper, "frequency"):
            return "Diaper changes follow a \(periodText) cycle"
        case (.health, "temperature"):
            return "Body temperature follows a \(periodText) cycle"
        default:
            return "\(category.displayName) \(metric) follows a \(periodText) cycle"
        }
    }

    /// Format a period in hours to a human-readable string
    /// - Parameter hours: The period in hours
    /// - Returns: Formatted period string
    private static func formatPeriod(_ hours: Double) -> String {
        if hours < 1 {
            return "\(Int(hours * 60))-minute"
        } else if hours == 1 {
            return "1-hour"
        } else if hours < 24 {
            return "\(Int(hours))-hour"
        } else if hours == 24 {
            return "daily"
        } else if hours == 48 {
            return "2-day"
        } else {
            return "\(Int(hours / 24))-day"
        }
    }

    // MARK: - Trend Analysis

    /// Detect trends in time series data
    /// - Parameters:
    ///   - timestamps: Array of timestamps
    ///   - values: Array of corresponding values
    ///   - category: The category being analyzed
    ///   - metric: The metric being analyzed
    /// - Returns: Trend analysis if detected, nil otherwise
    static func detectTrend(
        timestamps: [Date],
        values: [Double],
        category: Insight.InsightCategory,
        metric: String
    ) -> TrendAnalysis? {
        guard timestamps.count == values.count, timestamps.count >= 3 else {
            return nil
        }

        // Sort data points by timestamp
        let sortedData = zip(timestamps, values).sorted { $0.0 < $1.0 }
        let sortedTimestamps = sortedData.map { $0.0 }
        let sortedValues = sortedData.map { $0.1 }

        // Calculate time intervals in days
        var timeIntervalsInDays: [Double] = []
        for i in 0..<sortedTimestamps.count - 1 {
            let interval = sortedTimestamps[i + 1].timeIntervalSince(sortedTimestamps[i]) / (24 * 3600)
            timeIntervalsInDays.append(interval)
        }

        // Calculate value changes
        var valueChanges: [Double] = []
        for i in 0..<sortedValues.count - 1 {
            let change = sortedValues[i + 1] - sortedValues[i]
            valueChanges.append(change)
        }

        // Calculate rate of change per day
        var ratesOfChange: [Double] = []
        for i in 0..<valueChanges.count {
            let rate = valueChanges[i] / timeIntervalsInDays[i]
            ratesOfChange.append(rate)
        }

        // Calculate average rate of change
        let averageRateOfChange = ratesOfChange.reduce(0, +) / Double(ratesOfChange.count)

        // Determine trend direction and check for fluctuating pattern
        let rateVariance = ratesOfChange.reduce(0.0) { sum, rate in
            let diff = rate - averageRateOfChange
            return sum + (diff * diff)
        } / Double(ratesOfChange.count)

        let rateStdDev = sqrt(rateVariance)
        let coefficientOfVariation = abs(rateStdDev / averageRateOfChange)

        // Determine final direction
        let direction: TrendDirection
        if abs(averageRateOfChange) < 0.01 { // Threshold for "stable"
            direction = .stable
        } else if coefficientOfVariation > 1.5 {
            direction = .fluctuating
        } else if averageRateOfChange > 0 {
            direction = .increasing
        } else {
            direction = .decreasing
        }

        // Calculate sustained days
        let totalDays = sortedTimestamps.last!.timeIntervalSince(sortedTimestamps.first!) / (24 * 3600)
        let sustainedDays = max(1, Int(totalDays))

        // Calculate confidence
        let confidence: Double
        if direction == .stable {
            confidence = 70.0
        } else if direction == .fluctuating {
            confidence = 50.0
        } else {
            // Higher confidence for consistent trends
            confidence = min(100.0, max(50.0, 100.0 - coefficientOfVariation * 30.0))
        }

        return TrendAnalysis(
            category: category,
            metric: metric,
            direction: direction,
            rateOfChange: averageRateOfChange,
            sustainedDays: sustainedDays,
            confidence: confidence
        )
    }

    // MARK: - Anomaly Detection

    /// Detect anomalies in time series data
    /// - Parameters:
    ///   - value: The value to check
    ///   - historicalValues: Array of historical values
    ///   - category: The category being analyzed
    ///   - metric: The metric being analyzed
    /// - Returns: Anomaly detection if anomaly found, nil otherwise
    static func detectAnomaly(
        value: Double,
        historicalValues: [Double],
        category: Insight.InsightCategory,
        metric: String
    ) -> AnomalyDetection? {
        guard !historicalValues.isEmpty else {
            return nil
        }

        // Calculate mean and standard deviation
        let mean = historicalValues.reduce(0, +) / Double(historicalValues.count)
        let variance = historicalValues.reduce(0.0) { sum, val in
            let diff = val - mean
            return sum + (diff * diff)
        } / Double(historicalValues.count)
        let stdDev = sqrt(variance)

        // Calculate z-score (how many standard deviations from the mean)
        let zScore = stdDev > 0 ? (value - mean) / stdDev : 0

        // Determine if this is a significant anomaly
        // Typically, |z-score| > 2 is considered unusual
        let isSignificant = abs(zScore) >= 2.0

        // Only return if it's significant or if the z-score is at least 1.5
        guard isSignificant || abs(zScore) >= 1.5 else {
            return nil
        }

        return AnomalyDetection(
            category: category,
            metric: metric,
            expectedValue: mean,
            actualValue: value,
            deviationSigma: abs(zScore),
            isSignificant: isSignificant
        )
    }

    // MARK: - Sleep Pattern Detection

    /// Calculate day/night ratio for sleep entries
    /// - Parameter entries: Sleep entries to analyze
    /// - Returns: Ratio of daytime sleep to nighttime sleep
    static func calculateDayNightRatio(entries: [SleepEntry]) -> Double {
        let calendar = Calendar.current

        var daytimeSleepMinutes = 0.0
        var nighttimeSleepMinutes = 0.0

        for entry in entries {
            let hour = calendar.component(.hour, from: entry.timestamp)
            let duration = entry.duration

            // Consider 7am-7pm as daytime
            if hour >= 7 && hour < 19 {
                daytimeSleepMinutes += Double(duration ?? 0)
            } else {
                nighttimeSleepMinutes += Double(duration ?? 0)
            }
        }

        // Avoid division by zero
        if nighttimeSleepMinutes == 0 {
            return daytimeSleepMinutes > 0 ? 10.0 : 1.0
        }

        return daytimeSleepMinutes / nighttimeSleepMinutes
    }

    /// Calculate sleep consolidation score
    /// - Parameter entries: Sleep entries to analyze
    /// - Returns: Consolidation score (0-100)
    static func calculateSleepConsolidation(entries: [SleepEntry]) -> Int {
        guard !entries.isEmpty else { return 0 }

        // Sort entries by timestamp
        let sortedEntries = entries.sorted { $0.timestamp < $1.timestamp }

        // Calculate longest sleep stretch
        var longestStretch = 0.0
        for entry in sortedEntries {
            if let duration = entry.duration {
                longestStretch = max(longestStretch, Double(duration))
            }
        }

        // Calculate average duration
        let totalDuration = sortedEntries.reduce(0.0) { total, entry in
            if let duration = entry.duration {
                return total + Double(duration)
            }
            return total
        }
        // Calculate average duration (for future use)
        let _ = sortedEntries.isEmpty ? 0.0 : totalDuration / Double(sortedEntries.count)

        // Calculate time between sleeps
        var totalGapTime = 0.0
        var gapCount = 0

        for i in 0..<sortedEntries.count - 1 {
            guard let duration = sortedEntries[i].duration else { continue }
            let currentEnd = sortedEntries[i].timestamp.addingTimeInterval(Double(duration) * 60)
            let nextStart = sortedEntries[i + 1].timestamp

            if nextStart > currentEnd {
                let gap = nextStart.timeIntervalSince(currentEnd) / 60 // in minutes
                totalGapTime += gap
                gapCount += 1
            }
        }

        let averageGap = gapCount > 0 ? totalGapTime / Double(gapCount) : 0

        // Calculate consolidation score
        // Higher score means more consolidated sleep
        // Factors: longer stretches, fewer entries, longer gaps between sleeps

        let stretchFactor = min(100, longestStretch / 10 * 100) // 10+ hours = 100%
        let countFactor = min(100, max(0, 100 - Double(sortedEntries.count) * 10)) // Fewer entries = higher score
        let gapFactor = min(100, averageGap / 120 * 100) // 2+ hour gaps = 100%

        // Weight the factors
        let consolidationScore = Int((stretchFactor * 0.5) + (countFactor * 0.3) + (gapFactor * 0.2))

        return min(100, max(0, consolidationScore))
    }

    /// Detect sleep associations
    /// - Parameter entries: Sleep entries to analyze
    /// - Returns: Sleep association result, if any
    static func detectSleepAssociations(entries: [SleepEntry]) -> (method: String, confidence: Int)? {
        guard entries.count >= 3 else { return nil }

        // Count sleep locations instead of methods
        var locationCounts: [SleepEntry.SleepLocation: Int] = [:]

        for entry in entries {
            if let location = entry.location {
                locationCounts[location, default: 0] += 1
            }
        }

        // Find the most common location
        if let (location, count) = locationCounts.max(by: { $0.value < $1.value }) {
            let percentage = Double(count) / Double(entries.count) * 100

            // Only return if the location is used at least 50% of the time
            if percentage >= 50 {
                return (method: location.description, confidence: Int(percentage))
            }
        }

        return nil
    }

    // MARK: - Feeding Pattern Detection

    /// Detect feeding schedule pattern
    /// - Parameter entries: Feeding entries to analyze
    /// - Returns: Pattern result for feeding schedule
    static func detectFeedingSchedule(entries: [FeedingEntry]) -> PatternResult? {
        guard entries.count >= 5 else { return nil }

        // Sort entries by timestamp
        let sortedEntries = entries.sorted { $0.timestamp < $1.timestamp }

        // Calculate time between feedings
        var intervals: [TimeInterval] = []

        for i in 0..<sortedEntries.count - 1 {
            let interval = sortedEntries[i + 1].timestamp.timeIntervalSince(sortedEntries[i].timestamp)
            intervals.append(interval)
        }

        // Calculate average interval
        let averageInterval = intervals.reduce(0, +) / Double(intervals.count)

        // Calculate standard deviation
        let variance = intervals.reduce(0.0) { sum, interval in
            let diff = interval - averageInterval
            return sum + (diff * diff)
        } / Double(intervals.count)

        let standardDeviation = sqrt(variance)

        // Calculate coefficient of variation (lower means more consistent)
        let coefficientOfVariation = standardDeviation / averageInterval

        // Determine if there's a consistent schedule
        let isConsistent = coefficientOfVariation < 0.3 // Less than 30% variation

        // Calculate confidence
        let confidence = Int(min(100, max(0, 100 - coefficientOfVariation * 100)))

        // Format average interval
        let hours = Int(averageInterval / 3600)
        let minutes = Int((averageInterval.truncatingRemainder(dividingBy: 3600)) / 60)
        let intervalString = hours > 0 ? "\(hours)h \(minutes)m" : "\(minutes)m"

        // Create pattern result
        return PatternResult(
            type: isConsistent ? "consistent_feeding_schedule" : "variable_feeding_schedule",
            confidence: confidence,
            description: isConsistent
                ? "Consistent feeding schedule approximately every \(intervalString)"
                : "Variable feeding schedule with no clear pattern",
            metrics: [
                "average_interval_minutes": averageInterval / 60,
                "coefficient_of_variation": coefficientOfVariation,
                "is_consistent": isConsistent
            ]
        )
    }

    // MARK: - Diaper Pattern Detection

    /// Detect diaper frequency pattern
    /// - Parameter entries: Diaper entries to analyze
    /// - Returns: Pattern result for diaper frequency
    static func detectDiaperFrequencyPattern(entries: [DiaperEntry]) -> PatternResult? {
        guard entries.count >= 3 else { return nil }

        // Count by type
        let wetCount = entries.filter { $0.type == .wet }.count
        let dirtyCount = entries.filter { $0.type == .dirty }.count
        let mixedCount = entries.filter { $0.type == .mixed }.count

        // Calculate percentages
        let totalCount = entries.count
        let wetPercentage = Double(wetCount) / Double(totalCount) * 100
        let dirtyPercentage = Double(dirtyCount) / Double(totalCount) * 100
        let mixedPercentage = Double(mixedCount) / Double(totalCount) * 100

        // Determine primary type
        var primaryType = "mixed"
        var primaryPercentage = mixedPercentage

        if wetPercentage > dirtyPercentage && wetPercentage > mixedPercentage {
            primaryType = "wet"
            primaryPercentage = wetPercentage
        } else if dirtyPercentage > wetPercentage && dirtyPercentage > mixedPercentage {
            primaryType = "dirty"
            primaryPercentage = dirtyPercentage
        }

        // Calculate confidence
        let confidence = Int(min(100, max(50, primaryPercentage)))

        // Create pattern result
        return PatternResult(
            type: "diaper_type_distribution",
            confidence: confidence,
            description: "Primarily \(primaryType) diapers (\(Int(primaryPercentage))%)",
            metrics: [
                "wet_percentage": wetPercentage,
                "dirty_percentage": dirtyPercentage,
                "mixed_percentage": mixedPercentage,
                "primary_type": primaryType
            ]
        )
    }

    // MARK: - Growth Pattern Detection

    /// Detect growth velocity pattern
    /// - Parameter entries: Growth entries to analyze
    /// - Returns: Pattern result for growth velocity
    static func detectGrowthVelocity(entries: [GrowthEntry]) -> PatternResult? {
        // Filter entries with weight data
        let weightEntries = entries.filter { $0.weight != nil }

        guard weightEntries.count >= 2 else { return nil }

        // Sort entries by timestamp
        let sortedEntries = weightEntries.sorted { $0.timestamp < $1.timestamp }

        // Calculate weight changes
        var weightChanges: [Double] = []
        var timeIntervals: [TimeInterval] = []

        for i in 0..<sortedEntries.count - 1 {
            guard let currentWeight = sortedEntries[i].weight,
                  let nextWeight = sortedEntries[i + 1].weight else {
                continue
            }

            let weightChange = nextWeight - currentWeight
            let timeInterval = sortedEntries[i + 1].timestamp.timeIntervalSince(sortedEntries[i].timestamp)

            // Convert to grams per day
            let daysInterval = timeInterval / (24 * 3600)
            let gramsPerDay = (weightChange * 1000) / daysInterval

            weightChanges.append(gramsPerDay)
            timeIntervals.append(timeInterval)
        }

        guard !weightChanges.isEmpty else { return nil }

        // Calculate average growth velocity
        let averageVelocity = weightChanges.reduce(0, +) / Double(weightChanges.count)

        // Determine if growth is accelerating, steady, or decelerating
        var trendType = "steady"
        var trendConfidence = 50

        if weightChanges.count >= 3 {
            // Check if growth is consistently increasing or decreasing
            var increasingCount = 0
            var decreasingCount = 0

            for i in 0..<weightChanges.count - 1 {
                if weightChanges[i + 1] > weightChanges[i] {
                    increasingCount += 1
                } else if weightChanges[i + 1] < weightChanges[i] {
                    decreasingCount += 1
                }
            }

            let totalComparisons = weightChanges.count - 1
            let increasingPercentage = Double(increasingCount) / Double(totalComparisons) * 100
            let decreasingPercentage = Double(decreasingCount) / Double(totalComparisons) * 100

            if increasingPercentage >= 70 {
                trendType = "accelerating"
                trendConfidence = Int(increasingPercentage)
            } else if decreasingPercentage >= 70 {
                trendType = "decelerating"
                trendConfidence = Int(decreasingPercentage)
            }
        }

        // Create pattern result
        return PatternResult(
            type: "growth_velocity",
            confidence: trendConfidence,
            description: "\(trendType.capitalized) growth at \(Int(averageVelocity)) g/day",
            metrics: [
                "average_velocity_grams_per_day": averageVelocity,
                "trend_type": trendType
            ]
        )
    }
}
