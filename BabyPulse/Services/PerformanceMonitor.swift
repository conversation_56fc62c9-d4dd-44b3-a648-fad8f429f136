import Foundation
import SwiftUI
import os.log
import Combine

// MARK: - Performance Monitor
@MainActor
class PerformanceMonitor: ObservableObject {
    static let shared = PerformanceMonitor()
    
    // MARK: - Published Properties
    @Published var metrics: PerformanceMetrics = PerformanceMetrics()
    @Published var isMonitoring = false
    
    // MARK: - Private Properties
    private let logger = Logger(subsystem: "com.babypulse.app", category: "Performance")
    private var startTimes: [String: CFTimeInterval] = [:]
    private var memoryTimer: Timer?
    private var frameRateTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    // Memory tracking
    private var memoryUsageHistory: [MemoryUsage] = []
    private let maxHistoryCount = 100
    
    // Frame rate tracking
    private var frameCount = 0
    private var lastFrameTime = CACurrentMediaTime()
    private var frameRateHistory: [Double] = []
    
    private init() {
        setupMonitoring()
    }
    
    // MARK: - Public Methods
    
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        logger.info("Performance monitoring started")
        
        // Start memory monitoring
        memoryTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateMemoryMetrics()
            }
        }
        
        // Start frame rate monitoring
        frameRateTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateFrameRate()
            }
        }
    }
    
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        memoryTimer?.invalidate()
        frameRateTimer?.invalidate()
        logger.info("Performance monitoring stopped")
    }
    
    // MARK: - Timing Methods
    
    func startTiming(_ identifier: String) {
        startTimes[identifier] = CACurrentMediaTime()
        logger.debug("Started timing: \(identifier)")
    }
    
    func endTiming(_ identifier: String) -> TimeInterval? {
        guard let startTime = startTimes.removeValue(forKey: identifier) else {
            logger.warning("No start time found for identifier: \(identifier)")
            return nil
        }
        
        let duration = CACurrentMediaTime() - startTime
        logger.debug("Ended timing: \(identifier) - Duration: \(duration)s")
        
        // Update metrics
        updateTimingMetrics(identifier: identifier, duration: duration)
        
        return duration
    }
    
    func measureAsync<T>(_ identifier: String, operation: () async throws -> T) async rethrows -> T {
        startTiming(identifier)
        defer { _ = endTiming(identifier) }
        return try await operation()
    }
    
    func measure<T>(_ identifier: String, operation: () throws -> T) rethrows -> T {
        startTiming(identifier)
        defer { _ = endTiming(identifier) }
        return try operation()
    }
    
    // MARK: - Event Tracking
    
    func trackEvent(_ event: PerformanceEvent) {
        logger.info("Performance event: \(event.name) - \(event.description)")
        
        let eventRecord = EventRecord(
            event: event,
            timestamp: Date(),
            memoryUsage: getCurrentMemoryUsage()
        )
        
        metrics.events.append(eventRecord)
        
        // Keep only recent events
        if metrics.events.count > 50 {
            metrics.events.removeFirst()
        }
    }
    
    func trackUserInteraction(_ interaction: UserInteraction) {
        let interactionRecord = UserInteractionRecord(
            interaction: interaction,
            timestamp: Date(),
            responseTime: nil
        )
        
        metrics.userInteractions.append(interactionRecord)
        
        // Keep only recent interactions
        if metrics.userInteractions.count > 100 {
            metrics.userInteractions.removeFirst()
        }
        
        logger.debug("User interaction tracked: \(interaction.type)")
    }
    
    func trackViewLoad(_ viewName: String, loadTime: TimeInterval) {
        let viewLoad = ViewLoadMetric(
            viewName: viewName,
            loadTime: loadTime,
            timestamp: Date()
        )
        
        metrics.viewLoads.append(viewLoad)
        
        // Keep only recent view loads
        if metrics.viewLoads.count > 50 {
            metrics.viewLoads.removeFirst()
        }
        
        logger.info("View load tracked: \(viewName) - \(loadTime)s")
    }
    
    // MARK: - Memory Methods
    
    func getCurrentMemoryUsage() -> MemoryUsage {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let usedMB = Double(info.resident_size) / 1024 / 1024
            return MemoryUsage(
                timestamp: Date(),
                usedMemoryMB: usedMB,
                availableMemoryMB: Double(ProcessInfo.processInfo.physicalMemory) / 1024 / 1024
            )
        } else {
            return MemoryUsage(timestamp: Date(), usedMemoryMB: 0, availableMemoryMB: 0)
        }
    }
    
    // MARK: - Analytics Methods
    
    func generatePerformanceReport() -> PerformanceReport {
        let report = PerformanceReport(
            timestamp: Date(),
            averageMemoryUsage: calculateAverageMemoryUsage(),
            peakMemoryUsage: calculatePeakMemoryUsage(),
            averageFrameRate: calculateAverageFrameRate(),
            slowestOperations: getSlowestOperations(),
            mostFrequentEvents: getMostFrequentEvents(),
            viewLoadSummary: getViewLoadSummary()
        )
        
        logger.info("Performance report generated")
        return report
    }
    
    func exportMetrics() -> Data? {
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            return try encoder.encode(metrics)
        } catch {
            logger.error("Failed to export metrics: \(error)")
            return nil
        }
    }
    
    // MARK: - Private Methods
    
    private func setupMonitoring() {
        // Monitor app lifecycle events
        NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)
            .sink { [weak self] _ in
                self?.trackEvent(.appBecameActive)
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                self?.trackEvent(.appEnteredBackground)
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                self?.trackEvent(.memoryWarning)
            }
            .store(in: &cancellables)
    }
    
    private func updateMemoryMetrics() {
        let memoryUsage = getCurrentMemoryUsage()
        memoryUsageHistory.append(memoryUsage)
        
        if memoryUsageHistory.count > maxHistoryCount {
            memoryUsageHistory.removeFirst()
        }
        
        metrics.currentMemoryUsage = memoryUsage
        
        // Check for memory warnings
        if memoryUsage.usedMemoryMB > 200 { // 200MB threshold
            trackEvent(.highMemoryUsage(memoryUsage.usedMemoryMB))
        }
    }
    
    private func updateFrameRate() {
        frameCount += 1
        let currentTime = CACurrentMediaTime()
        let deltaTime = currentTime - lastFrameTime
        
        if deltaTime >= 1.0 {
            let fps = Double(frameCount) / deltaTime
            frameRateHistory.append(fps)
            
            if frameRateHistory.count > 60 {
                frameRateHistory.removeFirst()
            }
            
            metrics.currentFrameRate = fps
            frameCount = 0
            lastFrameTime = currentTime
            
            // Check for low frame rate
            if fps < 30 {
                trackEvent(.lowFrameRate(fps))
            }
        }
    }
    
    private func updateTimingMetrics(identifier: String, duration: TimeInterval) {
        if metrics.operationTimes[identifier] == nil {
            metrics.operationTimes[identifier] = []
        }
        
        metrics.operationTimes[identifier]?.append(duration)
        
        // Keep only recent measurements
        if let count = metrics.operationTimes[identifier]?.count, count > 20 {
            metrics.operationTimes[identifier]?.removeFirst()
        }
        
        // Check for slow operations
        if duration > 1.0 {
            trackEvent(.slowOperation(identifier, duration))
        }
    }
    
    private func calculateAverageMemoryUsage() -> Double {
        guard !memoryUsageHistory.isEmpty else { return 0 }
        return memoryUsageHistory.map { $0.usedMemoryMB }.reduce(0, +) / Double(memoryUsageHistory.count)
    }
    
    private func calculatePeakMemoryUsage() -> Double {
        return memoryUsageHistory.map { $0.usedMemoryMB }.max() ?? 0
    }
    
    private func calculateAverageFrameRate() -> Double {
        guard !frameRateHistory.isEmpty else { return 0 }
        return frameRateHistory.reduce(0, +) / Double(frameRateHistory.count)
    }
    
    private func getSlowestOperations() -> [(String, TimeInterval)] {
        return metrics.operationTimes.compactMap { (key, times) in
            guard let maxTime = times.max() else { return nil }
            return (key, maxTime)
        }.sorted { $0.1 > $1.1 }.prefix(5).map { $0 }
    }
    
    private func getMostFrequentEvents() -> [(PerformanceEvent, Int)] {
        let eventCounts = Dictionary(grouping: metrics.events) { $0.event.name }
            .mapValues { $0.count }
        
        return eventCounts.map { (PerformanceEvent.custom($0.key, ""), $0.value) }
            .sorted { $0.1 > $1.1 }
            .prefix(5)
            .map { $0 }
    }
    
    private func getViewLoadSummary() -> [String: (count: Int, averageTime: TimeInterval)] {
        let groupedLoads = Dictionary(grouping: metrics.viewLoads) { $0.viewName }
        
        return groupedLoads.mapValues { loads in
            let averageTime = loads.map { $0.loadTime }.reduce(0, +) / Double(loads.count)
            return (count: loads.count, averageTime: averageTime)
        }
    }
}

// MARK: - Performance Metrics
struct PerformanceMetrics: Codable {
    var currentMemoryUsage: MemoryUsage = MemoryUsage(timestamp: Date(), usedMemoryMB: 0, availableMemoryMB: 0)
    var currentFrameRate: Double = 0
    var operationTimes: [String: [TimeInterval]] = [:]
    var events: [EventRecord] = []
    var userInteractions: [UserInteractionRecord] = []
    var viewLoads: [ViewLoadMetric] = []
}

struct MemoryUsage: Codable {
    let timestamp: Date
    let usedMemoryMB: Double
    let availableMemoryMB: Double
    
    var usagePercentage: Double {
        guard availableMemoryMB > 0 else { return 0 }
        return (usedMemoryMB / availableMemoryMB) * 100
    }
}

struct EventRecord: Codable {
    let event: PerformanceEvent
    let timestamp: Date
    let memoryUsage: MemoryUsage
}

struct UserInteractionRecord: Codable {
    let interaction: UserInteraction
    let timestamp: Date
    let responseTime: TimeInterval?
}

struct ViewLoadMetric: Codable {
    let viewName: String
    let loadTime: TimeInterval
    let timestamp: Date
}

// MARK: - Performance Events
enum PerformanceEvent: Codable, Equatable {
    case appLaunch
    case appBecameActive
    case appEnteredBackground
    case memoryWarning
    case lowFrameRate(Double)
    case highMemoryUsage(Double)
    case slowOperation(String, TimeInterval)
    case viewDidAppear(String)
    case viewDidDisappear(String)
    case dataLoadStarted(String)
    case dataLoadCompleted(String, TimeInterval)
    case custom(String, String)
    
    var name: String {
        switch self {
        case .appLaunch: return "app_launch"
        case .appBecameActive: return "app_became_active"
        case .appEnteredBackground: return "app_entered_background"
        case .memoryWarning: return "memory_warning"
        case .lowFrameRate: return "low_frame_rate"
        case .highMemoryUsage: return "high_memory_usage"
        case .slowOperation: return "slow_operation"
        case .viewDidAppear: return "view_did_appear"
        case .viewDidDisappear: return "view_did_disappear"
        case .dataLoadStarted: return "data_load_started"
        case .dataLoadCompleted: return "data_load_completed"
        case .custom(let name, _): return name
        }
    }
    
    var description: String {
        switch self {
        case .appLaunch: return "Application launched"
        case .appBecameActive: return "Application became active"
        case .appEnteredBackground: return "Application entered background"
        case .memoryWarning: return "Memory warning received"
        case .lowFrameRate(let fps): return "Low frame rate: \(fps) FPS"
        case .highMemoryUsage(let mb): return "High memory usage: \(mb) MB"
        case .slowOperation(let op, let time): return "Slow operation: \(op) took \(time)s"
        case .viewDidAppear(let view): return "View appeared: \(view)"
        case .viewDidDisappear(let view): return "View disappeared: \(view)"
        case .dataLoadStarted(let source): return "Data load started: \(source)"
        case .dataLoadCompleted(let source, let time): return "Data load completed: \(source) in \(time)s"
        case .custom(_, let description): return description
        }
    }
}

// MARK: - User Interactions
struct UserInteraction: Codable {
    let type: InteractionType
    let target: String
    let context: [String: String]
    
    enum InteractionType: String, Codable {
        case tap, swipe, scroll, longPress, pinch, rotation
        case textInput, buttonPress, segmentedControlChange
        case navigationPush, navigationPop, tabSwitch
        case sheetPresentation, alertPresentation
    }
}

// MARK: - Performance Report
struct PerformanceReport: Codable {
    let timestamp: Date
    let averageMemoryUsage: Double
    let peakMemoryUsage: Double
    let averageFrameRate: Double
    let slowestOperations: [(String, TimeInterval)]
    let mostFrequentEvents: [(PerformanceEvent, Int)]
    let viewLoadSummary: [String: (count: Int, averageTime: TimeInterval)]
}

// MARK: - SwiftUI Integration
struct PerformanceTrackingModifier: ViewModifier {
    let viewName: String
    @StateObject private var monitor = PerformanceMonitor.shared
    
    func body(content: Content) -> some View {
        content
            .onAppear {
                monitor.startTiming("view_load_\(viewName)")
                monitor.trackEvent(.viewDidAppear(viewName))
            }
            .onDisappear {
                if let loadTime = monitor.endTiming("view_load_\(viewName)") {
                    monitor.trackViewLoad(viewName, loadTime: loadTime)
                }
                monitor.trackEvent(.viewDidDisappear(viewName))
            }
    }
}

extension View {
    func trackPerformance(viewName: String) -> some View {
        modifier(PerformanceTrackingModifier(viewName: viewName))
    }
} 