//
//  EnhancedInsightService.swift
//  BabyPulse
//
//  Created for BabyPulse app
//

import Foundation
import SwiftData
import SwiftUI

/// Enhanced service for generating insights from baby data
@MainActor
class EnhancedInsightService {
    private let modelContext: ModelContext
    private let promptTemplateManager = PromptTemplateManager.shared
    private let llmService = LLMService()

    // Data analyzers
    private let feedingAnalyzer = FeedingAnalyzer()
    private let sleepAnalyzer = SleepAnalyzer()
    private let diaperAnalyzer = DiaperAnalyzer()
    private let growthAnalyzer = GrowthAnalyzer()
    private let healthAnalyzer = HealthAnalyzer()

    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }

    // MARK: - Public Methods

    /// Generate insights for a baby using dynamic analysis of all entry types
    func generateInsights(for baby: Baby, completion: @escaping ([Insight]) -> Void) {
        Task {
            var allInsights: [Insight] = []

            // Generate feeding insights
            let feedingInsights = await generateFeedingInsights(for: baby)
            allInsights.append(contentsOf: feedingInsights)

            // Generate sleep insights
            let sleepInsights = await generateSleepInsights(for: baby)
            allInsights.append(contentsOf: sleepInsights)

            // Generate diaper insights
            let diaperInsights = await generateDiaperInsights(for: baby)
            allInsights.append(contentsOf: diaperInsights)

            // Generate growth insights
            let growthInsights = await generateGrowthInsights(for: baby)
            allInsights.append(contentsOf: growthInsights)

            // Generate health insights
            let healthInsights = await generateHealthInsights(for: baby)
            allInsights.append(contentsOf: healthInsights)

            // Sort insights by timestamp (newest first)
            let sortedInsights = allInsights.sorted(by: { $0.timestamp > $1.timestamp })

            // Return insights on the main thread
            await MainActor.run {
                completion(sortedInsights)
            }
        }
    }

    /// Generate insights when a new entry is added
    func generateInsightsForNewEntry(_ entry: Any, baby: Baby, completion: @escaping ([Insight]) -> Void) {
        Task {
            var insights: [Insight] = []

            // Handle different entry types
            if let feedingEntry = entry as? FeedingEntry {
                insights = await generateInsightsForNewFeedingEntry(feedingEntry, baby: baby)
            } else if let sleepEntry = entry as? SleepEntry {
                insights = await generateInsightsForNewSleepEntry(sleepEntry, baby: baby)
            } else if let diaperEntry = entry as? DiaperEntry {
                insights = await generateInsightsForNewDiaperEntry(diaperEntry, baby: baby)
            } else if let growthEntry = entry as? GrowthEntry {
                insights = await generateInsightsForNewGrowthEntry(growthEntry, baby: baby)
            } else if let healthEntry = entry as? HealthEntry {
                insights = await generateInsightsForNewHealthEntry(healthEntry, baby: baby)
            }

            // Return insights on the main thread
            await MainActor.run {
                completion(insights)
            }
        }
    }

    private func generateGrowthInsights(for baby: Baby) async -> [Insight] {
        // Fetch recent and historical growth entries
        let recentEntries = growthAnalyzer.fetchEntries(for: baby, inTimeRange: .last24Hours, modelContext: modelContext)
        let historicalEntries = growthAnalyzer.fetchEntries(for: baby, inTimeRange: .last7Days, modelContext: modelContext)

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Analyze the data
        let analysisResults = growthAnalyzer.analyzeData(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby)

        // Generate insights from analysis results
        var insights: [Insight] = []

        for result in analysisResults {
            // Only generate insights for results that exceed thresholds
            guard result.exceedsThreshold else { continue }

            // Get the appropriate template
            guard let template = promptTemplateManager.getTemplate(named: result.insightType) else { continue }

            // Generate insight using LLM
            if let insight = await generateInsightFromTemplate(template: template, variables: result.templateVariables, analysisResult: result, baby: baby) {
                insights.append(insight)
            }
        }

        return insights
    }

    private func generateHealthInsights(for baby: Baby) async -> [Insight] {
        // Fetch recent and historical health entries
        let recentEntries = healthAnalyzer.fetchEntries(for: baby, inTimeRange: .last24Hours, modelContext: modelContext)
        let historicalEntries = healthAnalyzer.fetchEntries(for: baby, inTimeRange: .last7Days, modelContext: modelContext)

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Analyze the data
        let analysisResults = healthAnalyzer.analyzeData(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby)

        // Generate insights from analysis results
        var insights: [Insight] = []

        for result in analysisResults {
            // Only generate insights for results that exceed thresholds
            guard result.exceedsThreshold else { continue }

            // Get the appropriate template
            guard let template = promptTemplateManager.getTemplate(named: result.insightType) else { continue }

            // Generate insight using LLM
            if let insight = await generateInsightFromTemplate(template: template, variables: result.templateVariables, analysisResult: result, baby: baby) {
                insights.append(insight)
            }
        }

        return insights
    }

    // MARK: - Private Methods for Generating Insights by Entry Type

    private func generateFeedingInsights(for baby: Baby) async -> [Insight] {
        // Fetch recent and historical feeding entries
        let recentEntries = feedingAnalyzer.fetchEntries(for: baby, inTimeRange: .last24Hours, modelContext: modelContext)
        let historicalEntries = feedingAnalyzer.fetchEntries(for: baby, inTimeRange: .last7Days, modelContext: modelContext)

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Analyze the data
        let analysisResults = feedingAnalyzer.analyzeData(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby)

        // Generate insights from analysis results
        var insights: [Insight] = []

        for result in analysisResults {
            // Only generate insights for results that exceed thresholds
            guard result.exceedsThreshold else { continue }

            // Get the appropriate template
            guard let template = promptTemplateManager.getTemplate(named: result.insightType) else { continue }

            // Generate insight using LLM
            if let insight = await generateInsightFromTemplate(template: template, variables: result.templateVariables, analysisResult: result, baby: baby) {
                insights.append(insight)
            }
        }

        return insights
    }

    private func generateSleepInsights(for baby: Baby) async -> [Insight] {
        // Fetch recent and historical sleep entries
        let recentEntries = sleepAnalyzer.fetchEntries(for: baby, inTimeRange: .last24Hours, modelContext: modelContext)
        let historicalEntries = sleepAnalyzer.fetchEntries(for: baby, inTimeRange: .last7Days, modelContext: modelContext)

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Analyze the data
        let analysisResults = sleepAnalyzer.analyzeData(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby)

        // Generate insights from analysis results
        var insights: [Insight] = []

        for result in analysisResults {
            // Only generate insights for results that exceed thresholds
            guard result.exceedsThreshold else { continue }

            // Get the appropriate template
            guard let template = promptTemplateManager.getTemplate(named: result.insightType) else { continue }

            // Generate insight using LLM
            if let insight = await generateInsightFromTemplate(template: template, variables: result.templateVariables, analysisResult: result, baby: baby) {
                insights.append(insight)
            }
        }

        return insights
    }

    private func generateDiaperInsights(for baby: Baby) async -> [Insight] {
        // Fetch recent and historical diaper entries
        let recentEntries = diaperAnalyzer.fetchEntries(for: baby, inTimeRange: .last24Hours, modelContext: modelContext)
        let historicalEntries = diaperAnalyzer.fetchEntries(for: baby, inTimeRange: .last7Days, modelContext: modelContext)

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Analyze the data
        let analysisResults = diaperAnalyzer.analyzeData(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby)

        // Generate insights from analysis results
        var insights: [Insight] = []

        for result in analysisResults {
            // Only generate insights for results that exceed thresholds
            guard result.exceedsThreshold else { continue }

            // Get the appropriate template
            guard let template = promptTemplateManager.getTemplate(named: result.insightType) else { continue }

            // Generate insight using LLM
            if let insight = await generateInsightFromTemplate(template: template, variables: result.templateVariables, analysisResult: result, baby: baby) {
                insights.append(insight)
            }
        }

        return insights
    }

    // MARK: - Private Methods for Generating Insights for New Entries

    private func generateInsightsForNewFeedingEntry(_ entry: FeedingEntry, baby: Baby) async -> [Insight] {
        // Fetch recent and historical feeding entries
        let recentEntries = feedingAnalyzer.fetchEntries(for: baby, inTimeRange: .last24Hours, modelContext: modelContext)
        let historicalEntries = feedingAnalyzer.fetchEntries(for: baby, inTimeRange: .last7Days, modelContext: modelContext)

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Analyze the data
        let analysisResults = feedingAnalyzer.analyzeData(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby)

        // Generate insights from analysis results
        var insights: [Insight] = []

        for result in analysisResults {
            // Only generate insights for results that exceed thresholds
            guard result.exceedsThreshold else { continue }

            // Get the appropriate template
            guard let template = promptTemplateManager.getTemplate(named: result.insightType) else { continue }

            // Generate insight using LLM
            if let insight = await generateInsightFromTemplate(template: template, variables: result.templateVariables, analysisResult: result, baby: baby) {
                insights.append(insight)
            }
        }

        return insights
    }

    private func generateInsightsForNewSleepEntry(_ entry: SleepEntry, baby: Baby) async -> [Insight] {
        // Fetch recent and historical sleep entries
        let recentEntries = sleepAnalyzer.fetchEntries(for: baby, inTimeRange: .last24Hours, modelContext: modelContext)
        let historicalEntries = sleepAnalyzer.fetchEntries(for: baby, inTimeRange: .last7Days, modelContext: modelContext)

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Analyze the data
        let analysisResults = sleepAnalyzer.analyzeData(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby)

        // Generate insights from analysis results
        var insights: [Insight] = []

        for result in analysisResults {
            // Only generate insights for results that exceed thresholds
            guard result.exceedsThreshold else { continue }

            // Get the appropriate template
            guard let template = promptTemplateManager.getTemplate(named: result.insightType) else { continue }

            // Generate insight using LLM
            if let insight = await generateInsightFromTemplate(template: template, variables: result.templateVariables, analysisResult: result, baby: baby) {
                insights.append(insight)
            }
        }

        return insights
    }

    private func generateInsightsForNewDiaperEntry(_ entry: DiaperEntry, baby: Baby) async -> [Insight] {
        // Fetch recent and historical diaper entries
        let recentEntries = diaperAnalyzer.fetchEntries(for: baby, inTimeRange: .last24Hours, modelContext: modelContext)
        let historicalEntries = diaperAnalyzer.fetchEntries(for: baby, inTimeRange: .last7Days, modelContext: modelContext)

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Analyze the data
        let analysisResults = diaperAnalyzer.analyzeData(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby)

        // Generate insights from analysis results
        var insights: [Insight] = []

        for result in analysisResults {
            // Only generate insights for results that exceed thresholds
            guard result.exceedsThreshold else { continue }

            // Get the appropriate template
            guard let template = promptTemplateManager.getTemplate(named: result.insightType) else { continue }

            // Generate insight using LLM
            if let insight = await generateInsightFromTemplate(template: template, variables: result.templateVariables, analysisResult: result, baby: baby) {
                insights.append(insight)
            }
        }

        return insights
    }

    private func generateInsightsForNewGrowthEntry(_ entry: GrowthEntry, baby: Baby) async -> [Insight] {
        // Fetch recent and historical growth entries
        let recentEntries = growthAnalyzer.fetchEntries(for: baby, inTimeRange: .last24Hours, modelContext: modelContext)
        let historicalEntries = growthAnalyzer.fetchEntries(for: baby, inTimeRange: .last7Days, modelContext: modelContext)

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Analyze the data
        let analysisResults = growthAnalyzer.analyzeData(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby)

        // Generate insights from analysis results
        var insights: [Insight] = []

        for result in analysisResults {
            // Only generate insights for results that exceed thresholds
            guard result.exceedsThreshold else { continue }

            // Get the appropriate template
            guard let template = promptTemplateManager.getTemplate(named: result.insightType) else { continue }

            // Generate insight using LLM
            if let insight = await generateInsightFromTemplate(template: template, variables: result.templateVariables, analysisResult: result, baby: baby) {
                insights.append(insight)
            }
        }

        return insights
    }

    private func generateInsightsForNewHealthEntry(_ entry: HealthEntry, baby: Baby) async -> [Insight] {
        // Fetch recent and historical health entries
        let recentEntries = healthAnalyzer.fetchEntries(for: baby, inTimeRange: .last24Hours, modelContext: modelContext)
        let historicalEntries = healthAnalyzer.fetchEntries(for: baby, inTimeRange: .last7Days, modelContext: modelContext)

        // Only proceed if we have enough data
        guard !recentEntries.isEmpty, !historicalEntries.isEmpty else {
            return []
        }

        // Analyze the data
        let analysisResults = healthAnalyzer.analyzeData(recentEntries: recentEntries, historicalEntries: historicalEntries, baby: baby)

        // Generate insights from analysis results
        var insights: [Insight] = []

        for result in analysisResults {
            // Only generate insights for results that exceed thresholds
            guard result.exceedsThreshold else { continue }

            // Get the appropriate template
            guard let template = promptTemplateManager.getTemplate(named: result.insightType) else { continue }

            // Generate insight using LLM
            if let insight = await generateInsightFromTemplate(template: template, variables: result.templateVariables, analysisResult: result, baby: baby) {
                insights.append(insight)
            }
        }

        return insights
    }

    // MARK: - Helper Methods

    private func generateInsightFromTemplate(template: PromptTemplate, variables: [String: String], analysisResult: AnalysisResult, baby: Baby) async -> Insight? {
        // Format the template with variables
        let formattedPrompt = promptTemplateManager.formatTemplate(template, with: variables)

        // Create baby context data from variables
        let babyContext = """
        Baby Name: \(variables["baby_name"] ?? "Unknown")
        Age: \(variables["baby_age"] ?? "Unknown") (\(variables["baby_age_weeks"] ?? "Unknown") weeks)
        Category: \(analysisResult.category.displayName)
        """

        // Generate response using LLM
        var generatedText = ""

        do {
            let result = try await withCheckedThrowingContinuation { continuation in
                llmService.generateResponse(userMessage: formattedPrompt, babyData: babyContext) { result in
                    continuation.resume(with: result)
                }
            }

            generatedText = result
        } catch {
            print("LLM generation error: \(error.localizedDescription)")
            // Fallback to a simple description if LLM fails
            generatedText = "Based on recent data, there appears to be a change in your baby's \(analysisResult.category.displayName.lowercased()) patterns."
        }

        // Create insight
        return Insight(
            id: UUID(),
            category: analysisResult.category,
            title: analysisResult.title,
            metric: analysisResult.metricString,
            insightContent: generatedText,
            timestamp: Date(),
            isRead: false,
            needsAttention: analysisResult.needsAttention,
            confidence: analysisResult.confidence,
            baby: baby
        )
    }
}
