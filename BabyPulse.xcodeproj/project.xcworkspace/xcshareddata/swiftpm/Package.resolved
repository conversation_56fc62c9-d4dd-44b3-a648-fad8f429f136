{"originHash": "898fe0dd1928f24a9b7d7ae0e1a236a3e049412cd50b32e326969a926e4c5eb6", "pins": [{"identity": "combine-schedulers", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/combine-schedulers", "state": {"revision": "5928286acce13def418ec36d05a001a9641086f2", "version": "1.0.3"}}, {"identity": "markdownview", "kind": "remoteSourceControl", "location": "https://github.com/keitaoouchi/MarkdownView.git", "state": {"revision": "5e62a27f1002cd63c350ef1a4c640ddcd1f1ed48", "version": "1.9.1"}}, {"identity": "purchases-ios-spm", "kind": "remoteSourceControl", "location": "https://github.com/RevenueCat/purchases-ios-spm.git", "state": {"revision": "31637c9d69c9f33a5426a6bfaca80d2bfe8950c1", "version": "5.23.0"}}, {"identity": "supabase-swift", "kind": "remoteSourceControl", "location": "https://github.com/supabase-community/supabase-swift.git", "state": {"revision": "4c4f2a0483a794e9294ae0f550a63881e4ee1de9", "version": "2.26.1"}}, {"identity": "swift-asn1", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-asn1.git", "state": {"revision": "a54383ada6cecde007d374f58f864e29370ba5c3", "version": "1.3.2"}}, {"identity": "swift-case-paths", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-case-paths", "state": {"revision": "41b89b8b68d8c56c622dbb7132258f1a3e638b25", "version": "1.7.0"}}, {"identity": "swift-clocks", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-clocks", "state": {"revision": "cc46202b53476d64e824e0b6612da09d84ffde8e", "version": "1.0.6"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections", "state": {"revision": "c1805596154bb3a265fd91b8ac0c4433b4348fb0", "version": "1.2.0"}}, {"identity": "swift-composable-architecture", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-composable-architecture", "state": {"revision": "294ac2cbfe48a41a6bd3c294fbb7bc5f0f5194d6", "version": "1.20.1"}}, {"identity": "swift-concurrency-extras", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-concurrency-extras", "state": {"revision": "82a4ae7170d98d8538ec77238b7eb8e7199ef2e8", "version": "1.3.1"}}, {"identity": "swift-crypto", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-crypto.git", "state": {"revision": "e8d6eba1fef23ae5b359c46b03f7d94be2f41fed", "version": "3.12.3"}}, {"identity": "swift-custom-dump", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-custom-dump", "state": {"revision": "82645ec760917961cfa08c9c0c7104a57a0fa4b1", "version": "1.3.3"}}, {"identity": "swift-dependencies", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-dependencies", "state": {"revision": "4c90d6b2b9bf0911af87b103bb40f41771891596", "version": "1.9.2"}}, {"identity": "swift-http-types", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-http-types.git", "state": {"revision": "a0a57e949a8903563aba4615869310c0ebf14c03", "version": "1.4.0"}}, {"identity": "swift-identified-collections", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-identified-collections", "state": {"revision": "****************************************", "version": "1.1.1"}}, {"identity": "swift-navigation", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-navigation", "state": {"revision": "db6bc9dbfed001f21e6728fd36413d9342c235b4", "version": "2.3.0"}}, {"identity": "swift-perception", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-perception", "state": {"revision": "d924c62a70fca5f43872f286dbd7cef0957f1c01", "version": "1.6.0"}}, {"identity": "swift-sharing", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-sharing", "state": {"revision": "75e846ee3159dc75b3a29bfc24b6ce5a557ddca9", "version": "2.5.2"}}, {"identity": "swift-syntax", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-syntax", "state": {"revision": "f99ae8aa18f0cf0d53481901f88a0991dc3bd4a2", "version": "601.0.1"}}, {"identity": "xctest-dynamic-overlay", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/xctest-dynamic-overlay", "state": {"revision": "39de59b2d47f7ef3ca88a039dff3084688fe27f4", "version": "1.5.2"}}, {"identity": "yams", "kind": "remoteSourceControl", "location": "https://github.com/jpsim/Yams.git", "state": {"revision": "b4b8042411dc7bbb696300a34a4bf3ba1b7ad19b", "version": "5.3.1"}}], "version": 3}